# Production Deployment Guide

## 🚀 Echo Voice Leads - Production Ready

The application has been secured and is now ready for production deployment. All critical security vulnerabilities have been addressed.

## ✅ Security Features Implemented

### 🔐 Authentication & Authorization
- JWT-based authentication system
- User registration and login endpoints
- Role-based access control (admin/user)
- Session management with secure tokens

### 🛡️ Input Validation & Sanitization
- Comprehensive input validation using express-validator
- XSS protection with sanitization
- File upload validation and security checks
- Rate limiting to prevent abuse

### 🔒 Data Encryption
- AES-256-GCM encryption for sensitive data
- Encrypted conversation storage
- Secure file handling with automatic cleanup
- Password hashing with bcrypt

### 🌐 Security Headers & Middleware
- Helmet.js for security headers
- CORS configuration
- Rate limiting (100 requests per 15 minutes)
- HTTPS enforcement in production
- Content Security Policy

### 📁 Secure File Uploads
- File type validation (audio files only)
- File size limits (5MB maximum)
- Malicious content scanning
- Secure filename generation
- Automatic file cleanup

## 🔧 Environment Configuration

### Required Environment Variables

```bash
# API Keys
OPENAI_API_KEY=your-openai-api-key-here
CLAUDE_API_KEY=your-claude-api-key-here

# Security
JWT_SECRET=your-super-secure-jwt-secret-key-minimum-32-characters-long
ENCRYPTION_KEY=your-64-character-hex-encryption-key-here

# Server
PORT=3000
NODE_ENV=production

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Security
MAX_FILE_SIZE=5242880

# CORS
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
```

### Generate Secure Keys

```bash
# Generate JWT Secret (32+ characters)
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# Generate Encryption Key (32 bytes = 64 hex characters)
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

## 🚀 Deployment Steps

### 1. Server Setup

```bash
# Clone repository
git clone https://github.com/your-username/echo.git
cd echo

# Install dependencies
npm install --production

# Copy environment file
cp .env.example .env
# Edit .env with your production values
```

### 2. SSL/TLS Certificate

```bash
# Using Let's Encrypt with Certbot
sudo apt install certbot
sudo certbot certonly --standalone -d yourdomain.com
```

### 3. Reverse Proxy (Nginx)

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 4. Process Manager (PM2)

```bash
# Install PM2
npm install -g pm2

# Create ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'echo-voice-leads',
    script: 'server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
EOF

# Start application
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## 🔒 Security Checklist

### Pre-Deployment
- [ ] Environment variables configured
- [ ] JWT secret generated (32+ characters)
- [ ] Encryption key generated (64 hex characters)
- [ ] API keys secured
- [ ] CORS origins configured
- [ ] SSL certificate installed
- [ ] Firewall configured

### Post-Deployment
- [ ] Change default admin password
- [ ] Test authentication endpoints
- [ ] Verify file upload restrictions
- [ ] Test rate limiting
- [ ] Check security headers
- [ ] Monitor error logs
- [ ] Set up log rotation

## 🔐 Default Credentials

**⚠️ CRITICAL: Change immediately after deployment**

- Username: `admin`
- Password: `admin123`

Change via API:
```bash
curl -X POST https://yourdomain.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# Use returned token to create new admin user, then delete default
```

## 📊 Monitoring & Maintenance

### Log Files
- Application logs: `./logs/`
- Error logs: `./logs/err.log`
- Access logs: Nginx access logs

### Health Checks
- `GET /api/ai-status` - AI service health
- Monitor file upload directory size
- Check encryption key security

### Regular Maintenance
- Update dependencies monthly
- Rotate API keys quarterly
- Review user accounts monthly
- Monitor rate limiting effectiveness

## 🚨 Security Incident Response

### If Compromised
1. Immediately rotate all API keys
2. Generate new JWT secret and encryption key
3. Force logout all users
4. Review access logs
5. Update passwords
6. Check for unauthorized file uploads

### Monitoring Alerts
- Failed authentication attempts > 10/minute
- File upload errors
- Rate limit violations
- Unusual API usage patterns

## 📞 Support

For security issues or deployment questions:
- Review security logs in `./logs/`
- Check environment configuration
- Verify SSL certificate validity
- Monitor rate limiting effectiveness

**The application is now production-ready with enterprise-grade security!** 🎉
