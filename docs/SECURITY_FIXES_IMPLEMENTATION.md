# Critical Security Fixes Implementation Plan

## Priority 1: Immediate Critical Fixes (Required Before Production)

### 1. Add Authentication & Authorization

```javascript
// Install required packages
npm install jsonwebtoken bcryptjs express-rate-limit helmet

// Add to server.js
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
});
app.use('/api/', limiter);

// JWT middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) return res.status(403).json({ error: 'Invalid token' });
    req.user = user;
    next();
  });
};

// Protect all API routes
app.use('/api/chat', authenticateToken);
app.use('/api/speech-to-text', authenticateToken);
app.use('/api/text-to-speech', authenticateToken);
```

### 2. Secure File Upload System

```javascript
const path = require('path');
const crypto = require('crypto');

// Enhanced file upload configuration
const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB max
    files: 1
  },
  fileFilter: (req, file, cb) => {
    // Strict audio file validation
    const allowedMimes = [
      'audio/wav', 
      'audio/mpeg', 
      'audio/mp4', 
      'audio/ogg',
      'audio/webm'
    ];
    
    const allowedExtensions = ['.wav', '.mp3', '.mp4', '.ogg', '.webm'];
    const fileExtension = path.extname(file.originalname).toLowerCase();
    
    if (allowedMimes.includes(file.mimetype) && 
        allowedExtensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only audio files allowed.'), false);
    }
  }
});

// Secure file handling
app.post('/api/speech-to-text', authenticateToken, upload.single('audio'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No audio file provided' });
    }

    // Generate secure filename
    const secureFilename = crypto.randomBytes(16).toString('hex');
    const fileExtension = path.extname(req.file.originalname);
    const secureFilePath = path.join('uploads', secureFilename + fileExtension);
    
    // Move file to secure location
    fs.renameSync(req.file.path, secureFilePath);
    
    // Validate file size again
    const stats = fs.statSync(secureFilePath);
    if (stats.size > 5 * 1024 * 1024) {
      fs.unlinkSync(secureFilePath);
      return res.status(400).json({ error: 'File too large' });
    }

    // Process file...
    const audioStream = fs.createReadStream(secureFilePath);
    const transcriptionResult = await aiService.speechToText(audioStream, {
      language: 'en'
    });

    // Always clean up file
    fs.unlinkSync(secureFilePath);

    res.json({
      transcription: transcriptionResult.text,
      success: true,
      provider: transcriptionResult.provider
    });

  } catch (error) {
    // Clean up file on error
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    console.error('Speech-to-text error:', error.message);
    res.status(500).json({ 
      error: 'Speech processing failed',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});
```

### 3. Input Validation & Sanitization

```javascript
const validator = require('validator');
const xss = require('xss');

// Input validation middleware
const validateInput = (req, res, next) => {
  const { message, leadData } = req.body;
  
  // Validate message
  if (!message || typeof message !== 'string') {
    return res.status(400).json({ error: 'Valid message required' });
  }
  
  if (message.length > 1000) {
    return res.status(400).json({ error: 'Message too long (max 1000 characters)' });
  }
  
  // Sanitize input
  req.body.message = xss(validator.escape(message));
  
  // Validate leadData if present
  if (leadData) {
    if (leadData.email && !validator.isEmail(leadData.email)) {
      return res.status(400).json({ error: 'Invalid email format' });
    }
    
    if (leadData.phone && !validator.isMobilePhone(leadData.phone)) {
      return res.status(400).json({ error: 'Invalid phone format' });
    }
  }
  
  next();
};

// Apply to chat endpoint
app.post('/api/chat', authenticateToken, validateInput, async (req, res) => {
  // ... existing chat logic
});
```

### 4. Encrypt Conversation Data

```javascript
const crypto = require('crypto');

class SecureConversationStorage {
  constructor() {
    this.algorithm = 'aes-256-gcm';
    this.secretKey = process.env.ENCRYPTION_KEY || crypto.randomBytes(32);
  }
  
  encrypt(text) {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, this.secretKey);
    cipher.setAAD(Buffer.from('conversation-data'));
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex')
    };
  }
  
  decrypt(encryptedData) {
    const decipher = crypto.createDecipher(this.algorithm, this.secretKey);
    decipher.setAAD(Buffer.from('conversation-data'));
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
    
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}

const secureStorage = new SecureConversationStorage();

// Secure conversation saving
async function saveConversationExchange(userMessage, aiResponse, leadData) {
  try {
    const conversationData = {
      timestamp: new Date().toISOString(),
      userMessage: userMessage,
      aiResponse: aiResponse,
      leadData: leadData || {},
      sessionId: req.user?.sessionId || 'anonymous'
    };
    
    // Encrypt sensitive data
    const encryptedData = secureStorage.encrypt(JSON.stringify(conversationData));
    
    // Save encrypted data
    const conversations = loadConversations();
    conversations.push(encryptedData);
    
    fs.writeFileSync(CONVERSATION_FILE, JSON.stringify(conversations, null, 2));
    
  } catch (error) {
    console.error('Error saving conversation:', error);
  }
}
```

### 5. Environment Security

```bash
# Add to .env
JWT_SECRET=your-super-secure-jwt-secret-key-here
ENCRYPTION_KEY=your-32-byte-encryption-key-here
NODE_ENV=production
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
MAX_FILE_SIZE=5242880
ALLOWED_ORIGINS=https://yourdomain.com
```

### 6. Security Headers & HTTPS

```javascript
// Enhanced security configuration
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://api.openai.com", "https://api.anthropic.com"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// CORS configuration
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true,
  optionsSuccessStatus: 200
}));

// Force HTTPS in production
if (process.env.NODE_ENV === 'production') {
  app.use((req, res, next) => {
    if (req.header('x-forwarded-proto') !== 'https') {
      res.redirect(`https://${req.header('host')}${req.url}`);
    } else {
      next();
    }
  });
}
```

### 7. Error Handling & Logging

```javascript
// Secure error handling
app.use((error, req, res, next) => {
  // Log error securely (without sensitive data)
  console.error('Application error:', {
    message: error.message,
    stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    timestamp: new Date().toISOString(),
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    url: req.url
  });
  
  // Send generic error response
  const statusCode = error.status || 500;
  const message = statusCode === 500 ? 'Internal server error' : error.message;
  
  res.status(statusCode).json({
    error: message,
    timestamp: new Date().toISOString()
  });
});
```

## Implementation Checklist

- [ ] Install security packages (`helmet`, `express-rate-limit`, `jsonwebtoken`, etc.)
- [ ] Add authentication middleware
- [ ] Implement secure file upload validation
- [ ] Add input sanitization and validation
- [ ] Encrypt conversation data storage
- [ ] Configure security headers
- [ ] Set up rate limiting
- [ ] Add secure error handling
- [ ] Configure environment variables
- [ ] Test all security measures
- [ ] Perform security audit
- [ ] Set up monitoring and logging

## Testing Security Fixes

```bash
# Test rate limiting
curl -X POST http://localhost:3000/api/chat -H "Content-Type: application/json" -d '{"message":"test"}' --repeat 101

# Test file upload validation
curl -X POST http://localhost:3000/api/speech-to-text -F "audio=@malicious.exe"

# Test input validation
curl -X POST http://localhost:3000/api/chat -H "Content-Type: application/json" -d '{"message":"<script>alert(1)</script>"}'
```

**CRITICAL**: Do not deploy to production until all security fixes are implemented and tested.
