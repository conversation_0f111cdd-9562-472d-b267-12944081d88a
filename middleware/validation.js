const { body, validationResult } = require('express-validator');
const validator = require('validator');
const xss = require('xss');

// XSS configuration
const xssOptions = {
  whiteList: {}, // No HTML tags allowed
  stripIgnoreTag: true,
  stripIgnoreTagBody: ['script']
};

class ValidationService {
  // Sanitize input to prevent XSS
  sanitizeInput(input) {
    if (typeof input !== 'string') return input;
    
    // Remove XSS attempts
    let sanitized = xss(input, xssOptions);
    
    // Additional sanitization
    sanitized = validator.escape(sanitized);
    
    return sanitized;
  }

  // Validate and sanitize message input
  validateMessage() {
    return [
      body('message')
        .notEmpty()
        .withMessage('Message is required')
        .isLength({ min: 1, max: 2000 })
        .withMessage('Message must be between 1 and 2000 characters')
        .custom((value) => {
          // Check for suspicious patterns
          const suspiciousPatterns = [
            /<script/i,
            /javascript:/i,
            /on\w+\s*=/i,
            /data:text\/html/i,
            /vbscript:/i
          ];
          
          for (const pattern of suspiciousPatterns) {
            if (pattern.test(value)) {
              throw new Error('Message contains potentially malicious content');
            }
          }
          
          return true;
        })
        .customSanitizer((value) => this.sanitizeInput(value))
    ];
  }

  // Validate lead data
  validateLeadData() {
    return [
      body('leadData.name')
        .optional()
        .isLength({ max: 100 })
        .withMessage('Name must be less than 100 characters')
        .customSanitizer((value) => this.sanitizeInput(value)),
      
      body('leadData.email')
        .optional()
        .isEmail()
        .withMessage('Invalid email format')
        .normalizeEmail(),
      
      body('leadData.phone')
        .optional()
        .isMobilePhone()
        .withMessage('Invalid phone number format'),
      
      body('leadData.company')
        .optional()
        .isLength({ max: 200 })
        .withMessage('Company name must be less than 200 characters')
        .customSanitizer((value) => this.sanitizeInput(value)),
      
      body('leadData.investmentExperience')
        .optional()
        .isIn(['beginner', 'intermediate', 'advanced', 'professional'])
        .withMessage('Invalid investment experience level'),
      
      body('leadData.investmentGoals')
        .optional()
        .isLength({ max: 500 })
        .withMessage('Investment goals must be less than 500 characters')
        .customSanitizer((value) => this.sanitizeInput(value))
    ];
  }

  // Validate conversation history
  validateConversationHistory() {
    return [
      body('conversationHistory')
        .optional()
        .isArray({ max: 50 })
        .withMessage('Conversation history must be an array with max 50 items'),
      
      body('conversationHistory.*.userMessage')
        .optional()
        .isLength({ max: 2000 })
        .withMessage('User message in history too long')
        .customSanitizer((value) => this.sanitizeInput(value)),
      
      body('conversationHistory.*.aiResponse')
        .optional()
        .isLength({ max: 5000 })
        .withMessage('AI response in history too long')
        .customSanitizer((value) => this.sanitizeInput(value))
    ];
  }

  // Validate user registration
  validateUserRegistration() {
    return [
      body('username')
        .isLength({ min: 3, max: 30 })
        .withMessage('Username must be between 3 and 30 characters')
        .matches(/^[a-zA-Z0-9_-]+$/)
        .withMessage('Username can only contain letters, numbers, underscores, and hyphens')
        .customSanitizer((value) => this.sanitizeInput(value)),
      
      body('email')
        .isEmail()
        .withMessage('Invalid email format')
        .normalizeEmail(),
      
      body('password')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
        .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
    ];
  }

  // Validate user login
  validateUserLogin() {
    return [
      body('username')
        .notEmpty()
        .withMessage('Username or email is required')
        .customSanitizer((value) => this.sanitizeInput(value)),
      
      body('password')
        .notEmpty()
        .withMessage('Password is required')
    ];
  }

  // Validate text for TTS
  validateTextToSpeech() {
    return [
      body('text')
        .notEmpty()
        .withMessage('Text is required')
        .isLength({ min: 1, max: 4000 })
        .withMessage('Text must be between 1 and 4000 characters')
        .custom((value) => {
          // Remove any potential SSML injection attempts
          const ssmlPatterns = [
            /<speak/i,
            /<voice/i,
            /<prosody/i,
            /<break/i,
            /<emphasis/i,
            /<say-as/i
          ];
          
          for (const pattern of ssmlPatterns) {
            if (pattern.test(value)) {
              throw new Error('Text contains potentially malicious SSML content');
            }
          }
          
          return true;
        })
        .customSanitizer((value) => this.sanitizeInput(value))
    ];
  }

  // Middleware to handle validation errors
  handleValidationErrors(req, res, next) {
    const errors = validationResult(req);
    
    if (!errors.isEmpty()) {
      const errorMessages = errors.array().map(error => ({
        field: error.path,
        message: error.msg,
        value: error.value
      }));
      
      return res.status(400).json({
        error: 'Validation failed',
        details: errorMessages,
        timestamp: new Date().toISOString()
      });
    }
    
    next();
  }

  // Rate limiting validation
  validateRateLimit(windowMs = 15 * 60 * 1000, max = 100) {
    const requests = new Map();
    
    return (req, res, next) => {
      const clientId = req.ip || req.connection.remoteAddress;
      const now = Date.now();
      const windowStart = now - windowMs;
      
      // Clean old entries
      for (const [id, timestamps] of requests.entries()) {
        const validTimestamps = timestamps.filter(time => time > windowStart);
        if (validTimestamps.length === 0) {
          requests.delete(id);
        } else {
          requests.set(id, validTimestamps);
        }
      }
      
      // Check current client
      const clientRequests = requests.get(clientId) || [];
      const recentRequests = clientRequests.filter(time => time > windowStart);
      
      if (recentRequests.length >= max) {
        return res.status(429).json({
          error: 'Too many requests',
          retryAfter: Math.ceil((recentRequests[0] + windowMs - now) / 1000),
          timestamp: new Date().toISOString()
        });
      }
      
      // Add current request
      recentRequests.push(now);
      requests.set(clientId, recentRequests);
      
      next();
    };
  }

  // File upload validation
  validateFileUpload(req, file, cb) {
    // Check file type
    const allowedMimes = [
      'audio/wav',
      'audio/mpeg', 
      'audio/mp4',
      'audio/ogg',
      'audio/webm',
      'audio/x-wav',
      'audio/vnd.wav'
    ];
    
    if (!allowedMimes.includes(file.mimetype)) {
      return cb(new Error('Invalid file type. Only audio files are allowed.'), false);
    }
    
    // Check file extension
    const allowedExtensions = ['.wav', '.mp3', '.mp4', '.ogg', '.webm'];
    const fileExtension = require('path').extname(file.originalname).toLowerCase();
    
    if (!allowedExtensions.includes(fileExtension)) {
      return cb(new Error('Invalid file extension.'), false);
    }
    
    // Additional security checks
    if (file.originalname.includes('..') || file.originalname.includes('/')) {
      return cb(new Error('Invalid file name.'), false);
    }
    
    cb(null, true);
  }
}

const validationService = new ValidationService();

module.exports = {
  validationService,
  validateMessage: validationService.validateMessage.bind(validationService),
  validateLeadData: validationService.validateLeadData.bind(validationService),
  validateConversationHistory: validationService.validateConversationHistory.bind(validationService),
  validateUserRegistration: validationService.validateUserRegistration.bind(validationService),
  validateUserLogin: validationService.validateUserLogin.bind(validationService),
  validateTextToSpeech: validationService.validateTextToSpeech.bind(validationService),
  handleValidationErrors: validationService.handleValidationErrors.bind(validationService),
  validateRateLimit: validationService.validateRateLimit.bind(validationService),
  validateFileUpload: validationService.validateFileUpload.bind(validationService)
};
