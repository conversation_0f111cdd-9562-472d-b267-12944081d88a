const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const fs = require('fs');
const path = require('path');

const USERS_FILE = path.join(__dirname, '..', 'data', 'users.json');

// Ensure data directory exists
const dataDir = path.dirname(USERS_FILE);
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

class AuthService {
  constructor() {
    this.jwtSecret = process.env.JWT_SECRET || this.generateSecretKey();
    this.tokenExpiry = process.env.JWT_EXPIRY || '24h';
    this.initializeUsers();
  }

  generateSecretKey() {
    const crypto = require('crypto');
    const secret = crypto.randomBytes(64).toString('hex');
    console.warn('⚠️ Using generated JWT secret. Set JWT_SECRET in production!');
    return secret;
  }

  initializeUsers() {
    if (!fs.existsSync(USERS_FILE)) {
      // Create default admin user for initial setup
      const defaultUsers = [{
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        password: bcrypt.hashSync('admin123', 10), // Change this in production!
        role: 'admin',
        createdAt: new Date().toISOString(),
        isActive: true
      }];
      
      fs.writeFileSync(USERS_FILE, JSON.stringify(defaultUsers, null, 2));
      console.log('🔐 Default admin user created: admin/admin123 (CHANGE PASSWORD!)');
    }
  }

  loadUsers() {
    try {
      if (fs.existsSync(USERS_FILE)) {
        const data = fs.readFileSync(USERS_FILE, 'utf8');
        return JSON.parse(data);
      }
      return [];
    } catch (error) {
      console.error('Error loading users:', error);
      return [];
    }
  }

  saveUsers(users) {
    try {
      fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));
    } catch (error) {
      console.error('Error saving users:', error);
      throw new Error('Failed to save user data');
    }
  }

  async authenticateUser(username, password) {
    const users = this.loadUsers();
    const user = users.find(u => 
      (u.username === username || u.email === username) && u.isActive
    );

    if (!user) {
      return null;
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return null;
    }

    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        username: user.username,
        email: user.email,
        role: user.role 
      },
      this.jwtSecret,
      { expiresIn: this.tokenExpiry }
    );

    return {
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      }
    };
  }

  async createUser(userData) {
    const users = this.loadUsers();
    
    // Check if user already exists
    const existingUser = users.find(u => 
      u.username === userData.username || u.email === userData.email
    );
    
    if (existingUser) {
      throw new Error('User already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(userData.password, 10);

    const newUser = {
      id: Date.now().toString(),
      username: userData.username,
      email: userData.email,
      password: hashedPassword,
      role: userData.role || 'user',
      createdAt: new Date().toISOString(),
      isActive: true
    };

    users.push(newUser);
    this.saveUsers(users);

    return {
      id: newUser.id,
      username: newUser.username,
      email: newUser.email,
      role: newUser.role
    };
  }

  verifyToken(token) {
    try {
      return jwt.verify(token, this.jwtSecret);
    } catch (error) {
      return null;
    }
  }

  // Middleware for protecting routes
  authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({ 
        error: 'Access token required',
        code: 'NO_TOKEN'
      });
    }

    const decoded = this.verifyToken(token);
    if (!decoded) {
      return res.status(403).json({ 
        error: 'Invalid or expired token',
        code: 'INVALID_TOKEN'
      });
    }

    req.user = decoded;
    next();
  }

  // Middleware for role-based access
  requireRole(role) {
    return (req, res, next) => {
      if (!req.user) {
        return res.status(401).json({ error: 'Authentication required' });
      }

      if (req.user.role !== role && req.user.role !== 'admin') {
        return res.status(403).json({ error: 'Insufficient permissions' });
      }

      next();
    };
  }

  // Optional authentication (for public endpoints that can benefit from user context)
  optionalAuth(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const decoded = this.verifyToken(token);
      if (decoded) {
        req.user = decoded;
      }
    }

    next();
  }
}

// Create singleton instance
const authService = new AuthService();

module.exports = {
  authService,
  authenticateToken: authService.authenticateToken.bind(authService),
  requireRole: authService.requireRole.bind(authService),
  optionalAuth: authService.optionalAuth.bind(authService)
};
