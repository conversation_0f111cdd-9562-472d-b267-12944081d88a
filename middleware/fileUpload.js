const multer = require('multer');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const { validateFileUpload } = require('./validation');

class SecureFileUpload {
  constructor() {
    this.uploadDir = path.join(__dirname, '..', 'uploads');
    this.maxFileSize = parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024; // 5MB default
    this.allowedMimeTypes = [
      'audio/wav',
      'audio/mpeg', 
      'audio/mp4',
      'audio/ogg',
      'audio/webm',
      'audio/x-wav',
      'audio/vnd.wav'
    ];
    this.allowedExtensions = ['.wav', '.mp3', '.mp4', '.ogg', '.webm'];
    
    this.ensureUploadDirectory();
  }

  ensureUploadDirectory() {
    if (!fs.existsSync(this.uploadDir)) {
      fs.mkdirSync(this.uploadDir, { recursive: true, mode: 0o755 });
    }
  }

  // Generate secure filename
  generateSecureFilename(originalName) {
    const timestamp = Date.now();
    const randomBytes = crypto.randomBytes(8).toString('hex');
    const extension = path.extname(originalName).toLowerCase();
    
    // Validate extension
    if (!this.allowedExtensions.includes(extension)) {
      throw new Error('Invalid file extension');
    }
    
    return `${timestamp}_${randomBytes}${extension}`;
  }

  // Validate file content (basic magic number check)
  validateFileContent(filePath, mimetype) {
    try {
      const buffer = fs.readFileSync(filePath);
      const magicNumbers = {
        'audio/wav': [0x52, 0x49, 0x46, 0x46], // RIFF
        'audio/mpeg': [0xFF, 0xFB], // MP3
        'audio/mp4': [0x00, 0x00, 0x00], // MP4 (partial)
        'audio/ogg': [0x4F, 0x67, 0x67, 0x53], // OggS
        'audio/webm': [0x1A, 0x45, 0xDF, 0xA3] // WebM
      };

      // Check for WAV
      if (mimetype.includes('wav')) {
        const wavHeader = Array.from(buffer.slice(0, 4));
        if (JSON.stringify(wavHeader) === JSON.stringify(magicNumbers['audio/wav'])) {
          return true;
        }
      }

      // Check for MP3
      if (mimetype.includes('mpeg') || mimetype.includes('mp3')) {
        const mp3Header = Array.from(buffer.slice(0, 2));
        if (JSON.stringify(mp3Header) === JSON.stringify(magicNumbers['audio/mpeg'])) {
          return true;
        }
      }

      // Check for OGG
      if (mimetype.includes('ogg')) {
        const oggHeader = Array.from(buffer.slice(0, 4));
        if (JSON.stringify(oggHeader) === JSON.stringify(magicNumbers['audio/ogg'])) {
          return true;
        }
      }

      // For other formats, do basic validation
      if (buffer.length > 44) { // Minimum audio file size
        return true;
      }

      return false;
    } catch (error) {
      console.error('File content validation error:', error);
      return false;
    }
  }

  // Scan for malicious content (basic implementation)
  scanForMaliciousContent(filePath) {
    try {
      const buffer = fs.readFileSync(filePath);
      const content = buffer.toString('utf8', 0, Math.min(buffer.length, 1024));
      
      // Check for suspicious patterns
      const maliciousPatterns = [
        /<script/i,
        /javascript:/i,
        /vbscript:/i,
        /on\w+\s*=/i,
        /data:text\/html/i,
        /<?php/i,
        /<%/i,
        /\$_GET/i,
        /\$_POST/i,
        /eval\(/i,
        /exec\(/i,
        /system\(/i
      ];

      for (const pattern of maliciousPatterns) {
        if (pattern.test(content)) {
          return false;
        }
      }

      return true;
    } catch (error) {
      // If we can't read as text, it's likely a binary file (which is good for audio)
      return true;
    }
  }

  // Create multer configuration
  createMulterConfig() {
    const storage = multer.diskStorage({
      destination: (req, file, cb) => {
        cb(null, this.uploadDir);
      },
      filename: (req, file, cb) => {
        try {
          const secureFilename = this.generateSecureFilename(file.originalname);
          cb(null, secureFilename);
        } catch (error) {
          cb(error);
        }
      }
    });

    const fileFilter = (req, file, cb) => {
      // Validate file type
      if (!this.allowedMimeTypes.includes(file.mimetype)) {
        return cb(new Error('Invalid file type. Only audio files are allowed.'), false);
      }

      // Validate file extension
      const extension = path.extname(file.originalname).toLowerCase();
      if (!this.allowedExtensions.includes(extension)) {
        return cb(new Error('Invalid file extension.'), false);
      }

      // Check filename for path traversal
      if (file.originalname.includes('..') || 
          file.originalname.includes('/') || 
          file.originalname.includes('\\')) {
        return cb(new Error('Invalid filename.'), false);
      }

      // Check filename length
      if (file.originalname.length > 255) {
        return cb(new Error('Filename too long.'), false);
      }

      cb(null, true);
    };

    return multer({
      storage,
      fileFilter,
      limits: {
        fileSize: this.maxFileSize,
        files: 1,
        fields: 10,
        fieldNameSize: 100,
        fieldSize: 1024
      }
    });
  }

  // Middleware for additional security checks
  additionalSecurityChecks(req, res, next) {
    if (!req.file) {
      return next();
    }

    const filePath = req.file.path;

    try {
      // Validate file size again
      const stats = fs.statSync(filePath);
      if (stats.size > this.maxFileSize) {
        fs.unlinkSync(filePath);
        return res.status(400).json({ 
          error: 'File too large',
          maxSize: this.maxFileSize 
        });
      }

      // Validate file content
      if (!this.validateFileContent(filePath, req.file.mimetype)) {
        fs.unlinkSync(filePath);
        return res.status(400).json({ 
          error: 'Invalid file content' 
        });
      }

      // Scan for malicious content
      if (!this.scanForMaliciousContent(filePath)) {
        fs.unlinkSync(filePath);
        return res.status(400).json({ 
          error: 'File contains potentially malicious content' 
        });
      }

      // Add security metadata to request
      req.file.securityChecked = true;
      req.file.uploadTime = new Date().toISOString();
      req.file.clientIP = req.ip;

      next();
    } catch (error) {
      // Clean up file on error
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
      
      console.error('File security check error:', error);
      res.status(500).json({ 
        error: 'File security validation failed' 
      });
    }
  }

  // Clean up old files
  cleanupOldFiles(maxAgeHours = 24) {
    try {
      const files = fs.readdirSync(this.uploadDir);
      const now = Date.now();
      const maxAge = maxAgeHours * 60 * 60 * 1000;

      for (const file of files) {
        const filePath = path.join(this.uploadDir, file);
        const stats = fs.statSync(filePath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          fs.unlinkSync(filePath);
          console.log(`Cleaned up old file: ${file}`);
        }
      }
    } catch (error) {
      console.error('File cleanup error:', error);
    }
  }

  // Secure file deletion
  secureDelete(filePath) {
    try {
      if (fs.existsSync(filePath)) {
        // Overwrite file with random data before deletion (basic secure delete)
        const stats = fs.statSync(filePath);
        const randomData = crypto.randomBytes(stats.size);
        fs.writeFileSync(filePath, randomData);
        fs.unlinkSync(filePath);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Secure delete error:', error);
      return false;
    }
  }

  // Get upload statistics
  getUploadStats() {
    try {
      const files = fs.readdirSync(this.uploadDir);
      const stats = {
        totalFiles: files.length,
        totalSize: 0,
        oldestFile: null,
        newestFile: null
      };

      for (const file of files) {
        const filePath = path.join(this.uploadDir, file);
        const fileStats = fs.statSync(filePath);
        stats.totalSize += fileStats.size;
        
        if (!stats.oldestFile || fileStats.mtime < stats.oldestFile) {
          stats.oldestFile = fileStats.mtime;
        }
        
        if (!stats.newestFile || fileStats.mtime > stats.newestFile) {
          stats.newestFile = fileStats.mtime;
        }
      }

      return stats;
    } catch (error) {
      console.error('Upload stats error:', error);
      return null;
    }
  }
}

// Create singleton instance
const secureFileUpload = new SecureFileUpload();

// Start cleanup interval (every hour)
setInterval(() => {
  secureFileUpload.cleanupOldFiles();
}, 60 * 60 * 1000);

module.exports = {
  secureFileUpload,
  upload: secureFileUpload.createMulterConfig(),
  additionalSecurityChecks: secureFileUpload.additionalSecurityChecks.bind(secureFileUpload),
  secureDelete: secureFileUpload.secureDelete.bind(secureFileUpload)
};
