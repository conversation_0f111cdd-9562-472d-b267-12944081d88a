const bcrypt = require('bcryptjs');
const { validationResult, body } = require('express-validator');

class AccessControlService {
  constructor() {
    this.accessPassword = process.env.ACCESS_PASSWORD || 'echo2025';
    this.sessionTimeout =
      parseInt(process.env.ACCESS_SESSION_TIMEOUT) || 24 * 60 * 60 * 1000; // 24 hours
    this.activeSessions = new Map(); // Store active sessions in memory

    console.log('🔐 Access control initialized');
    if (!process.env.ACCESS_PASSWORD) {
      console.warn(
        '⚠️ Using default access password. Set ACCESS_PASSWORD in production!'
      );
    }
  }

  // Generate session token for access
  generateAccessToken() {
    const crypto = require('crypto');
    return crypto.randomBytes(32).toString('hex');
  }

  // Validate access password
  async validateAccessPassword(password) {
    // Simple string comparison for now (can be enhanced with hashing if needed)
    return password === this.accessPassword;
  }

  // Create access session
  createAccessSession(req) {
    const sessionToken = this.generateAccessToken();
    const expiresAt = Date.now() + this.sessionTimeout;

    this.activeSessions.set(sessionToken, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      createdAt: Date.now(),
      expiresAt: expiresAt,
    });

    // Clean up expired sessions
    this.cleanupExpiredSessions();

    return sessionToken;
  }

  // Check if session is valid
  isValidAccessSession(sessionToken) {
    if (!sessionToken) return false;

    const session = this.activeSessions.get(sessionToken);
    if (!session) return false;

    if (Date.now() > session.expiresAt) {
      this.activeSessions.delete(sessionToken);
      return false;
    }

    return true;
  }

  // Clean up expired sessions
  cleanupExpiredSessions() {
    const now = Date.now();
    for (const [token, session] of this.activeSessions.entries()) {
      if (now > session.expiresAt) {
        this.activeSessions.delete(token);
      }
    }
  }

  // Middleware to check access password
  requireAccessPassword(req, res, next) {
    // Skip access control in development for Safari compatibility
    if (
      process.env.NODE_ENV === 'development' &&
      process.env.SKIP_ACCESS_CONTROL === 'true'
    ) {
      return next();
    }

    // Skip access control for certain paths
    const skipPaths = [
      '/api/access/verify',
      '/api/access/status',
      '/access.html',
      '/access.js',
      '/access.css',
    ];

    if (skipPaths.some((path) => req.path === path)) {
      return next();
    }

    // Check for access session token in cookie or header
    const accessToken =
      req.cookies?.accessToken || req.headers['x-access-token'];

    if (this.isValidAccessSession(accessToken)) {
      return next();
    }

    // If requesting API endpoints, return JSON error
    if (req.path.startsWith('/api/')) {
      return res.status(401).json({
        error: 'Access password required',
        code: 'ACCESS_REQUIRED',
        timestamp: new Date().toISOString(),
      });
    }

    // For web requests, redirect to access page
    res.redirect('/access.html');
  }

  // Validation for access password
  validateAccessPasswordInput() {
    return [
      body('password')
        .notEmpty()
        .withMessage('Access password is required')
        .isLength({ min: 1, max: 100 })
        .withMessage('Password must be between 1 and 100 characters'),
    ];
  }

  // Handle validation errors
  handleValidationErrors(req, res, next) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array().map((error) => ({
          field: error.path,
          message: error.msg,
        })),
        timestamp: new Date().toISOString(),
      });
    }

    next();
  }

  // Get access statistics
  getAccessStats() {
    this.cleanupExpiredSessions();

    return {
      activeSessions: this.activeSessions.size,
      sessionTimeout: this.sessionTimeout,
      hasCustomPassword: !!process.env.ACCESS_PASSWORD,
    };
  }

  // Revoke access session
  revokeAccessSession(sessionToken) {
    return this.activeSessions.delete(sessionToken);
  }

  // Revoke all sessions
  revokeAllSessions() {
    const count = this.activeSessions.size;
    this.activeSessions.clear();
    return count;
  }
}

// Create singleton instance
const accessControlService = new AccessControlService();

// Clean up expired sessions every hour
setInterval(() => {
  accessControlService.cleanupExpiredSessions();
}, 60 * 60 * 1000);

module.exports = {
  accessControlService,
  requireAccessPassword:
    accessControlService.requireAccessPassword.bind(accessControlService),
  validateAccessPasswordInput:
    accessControlService.validateAccessPasswordInput.bind(accessControlService),
  handleValidationErrors:
    accessControlService.handleValidationErrors.bind(accessControlService),
};
