const { detectStocksInText, getStockInfo, searchStocks, stockDetectionService } = require('./services/stockDetectionService');

/**
 * Test the stock detection system with various AI response examples
 */
function testStockDetection() {
  console.log('🧪 Testing Stock Detection System\n');
  
  // Test cases with different types of stock mentions
  const testCases = [
    {
      name: "Ticker Symbols",
      text: "I think AAPL and TSLA are good investments. You should also consider $NVDA for AI exposure."
    },
    {
      name: "Company Names",
      text: "Apple Inc. reported strong earnings, while Tesla continues to innovate in the EV space. Spotify is growing its podcast business."
    },
    {
      name: "Mixed References",
      text: "The tech giants like Apple, Microsoft, and GOOGL are dominating. Don't forget about Palantir (PLTR) in the AI space."
    },
    {
      name: "Investment Discussion",
      text: "For your portfolio, I'd recommend diversifying across Tesla for EV exposure, NVIDIA for AI chips, and maybe some Coinbase for crypto exposure."
    },
    {
      name: "Market Analysis",
      text: "Recent discussions show Apple benefiting from India tariff exemptions, while Serica Energy offers strong cash flow in the oil sector."
    },
    {
      name: "Biotech Focus",
      text: "Cardiol Therapeutics showed promising Phase 2 results for acute myocarditis treatment with their CardiolRx drug."
    },
    {
      name: "No Stocks",
      text: "The market is volatile today. Interest rates and inflation are key concerns for investors."
    },
    {
      name: "Edge Cases",
      text: "I like SPOT music streaming and Meta's metaverse vision. Facebook's rebranding to Meta was strategic."
    }
  ];

  testCases.forEach((testCase, index) => {
    console.log(`\n📝 Test ${index + 1}: ${testCase.name}`);
    console.log(`Text: "${testCase.text}"`);
    
    const detectedStocks = detectStocksInText(testCase.text);
    
    if (detectedStocks.length === 0) {
      console.log('❌ No stocks detected');
    } else {
      console.log(`✅ Detected ${detectedStocks.length} stock(s):`);
      
      detectedStocks.forEach(stock => {
        console.log(`   📊 ${stock.ticker} - ${stock.companyName}`);
        console.log(`      💰 Price: $${stock.price} (${stock.changePercent > 0 ? '+' : ''}${stock.changePercent}%)`);
        console.log(`      🎯 Target: $${stock.priceTarget}`);
        console.log(`      🔍 Matched: ${stock.matchDetails.matchedTerms.join(', ')}`);
        console.log(`      📈 Confidence: ${(stock.matchDetails.confidence * 100).toFixed(1)}%`);
        console.log(`      🏷️ Types: ${stock.matchDetails.matchTypes.join(', ')}`);
      });
    }
  });
}

/**
 * Test individual stock lookup
 */
function testStockLookup() {
  console.log('\n\n🔍 Testing Stock Lookup\n');
  
  const testTickers = ['AAPL', 'tsla', 'NVDA', 'INVALID'];
  
  testTickers.forEach(ticker => {
    const stock = getStockInfo(ticker);
    if (stock) {
      console.log(`✅ ${ticker}: ${stock.companyName} - $${stock.price}`);
    } else {
      console.log(`❌ ${ticker}: Not found`);
    }
  });
}

/**
 * Test stock search functionality
 */
function testStockSearch() {
  console.log('\n\n🔎 Testing Stock Search\n');
  
  const searchQueries = ['apple', 'ai', 'electric', 'crypto', 'music'];
  
  searchQueries.forEach(query => {
    const results = searchStocks(query);
    console.log(`🔍 Search "${query}": ${results.length} results`);
    results.forEach(stock => {
      console.log(`   📊 ${stock.ticker} - ${stock.companyName}`);
    });
  });
}

/**
 * Test service statistics
 */
function testServiceStats() {
  console.log('\n\n📊 Service Statistics\n');
  
  const stats = stockDetectionService.getStats();
  console.log(`📈 Total Stocks: ${stats.totalStocks}`);
  console.log(`🔑 Total Keywords: ${stats.totalKeywords}`);
  console.log(`📅 Last Updated: ${stats.lastUpdated}`);
  console.log(`🏷️ Version: ${stats.version}`);
}

/**
 * Performance test with longer text
 */
function testPerformance() {
  console.log('\n\n⚡ Performance Test\n');
  
  const longText = `
    In today's market analysis, we're seeing strong performance from Apple (AAPL) 
    following their latest iPhone launch. Tesla continues to lead the EV revolution, 
    while NVIDIA dominates the AI chip market. Spotify is expanding its podcast 
    offerings, and Meta is investing heavily in the metaverse. Palantir's government 
    contracts are driving growth, while Coinbase benefits from crypto adoption. 
    Qualcomm's 5G technology remains crucial for mobile innovation. In the energy 
    sector, Serica Energy provides exposure to North Sea oil and gas. Finally, 
    Cardiol Therapeutics represents an interesting biotech play in heart disease 
    treatment. The market sentiment around these stocks varies, but each offers 
    unique investment opportunities for different risk profiles and investment 
    strategies. Apple and Microsoft remain blue-chip favorites, while Tesla and 
    NVIDIA offer growth potential in emerging technologies.
  `.repeat(10); // Repeat to make it longer
  
  const startTime = Date.now();
  const detectedStocks = detectStocksInText(longText);
  const endTime = Date.now();
  
  console.log(`📝 Text length: ${longText.length} characters`);
  console.log(`⏱️ Detection time: ${endTime - startTime}ms`);
  console.log(`📊 Stocks detected: ${detectedStocks.length}`);
  console.log(`🎯 Unique stocks: ${new Set(detectedStocks.map(s => s.ticker)).size}`);
}

// Run all tests
if (require.main === module) {
  try {
    testStockDetection();
    testStockLookup();
    testStockSearch();
    testServiceStats();
    testPerformance();
    
    console.log('\n\n✅ All tests completed successfully!');
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

module.exports = {
  testStockDetection,
  testStockLookup,
  testStockSearch,
  testServiceStats,
  testPerformance
};
