/* Access Control Page Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.access-container {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
}

.access-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.logo {
    text-align: center;
    margin-bottom: 30px;
}

.logo h1 {
    color: #333;
    font-size: 2.5em;
    font-weight: 700;
    margin-bottom: 10px;
}

.subtitle {
    color: #666;
    font-size: 1.1em;
    font-weight: 500;
}

.access-form {
    margin-bottom: 30px;
}

.access-form h2 {
    color: #333;
    font-size: 1.8em;
    margin-bottom: 10px;
    text-align: center;
}

.description {
    color: #666;
    text-align: center;
    margin-bottom: 25px;
    font-size: 1.1em;
}

.input-group {
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    color: #333;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 1em;
}

.input-group input {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1.1em;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
}

.access-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.access-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.access-btn:active {
    transform: translateY(0);
}

.access-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message {
    background: #fee;
    color: #c33;
    padding: 12px;
    border-radius: 8px;
    margin-top: 15px;
    border: 1px solid #fcc;
    text-align: center;
    font-weight: 500;
}

.success-message {
    background: #efe;
    color: #363;
    padding: 12px;
    border-radius: 8px;
    margin-top: 15px;
    border: 1px solid #cfc;
    text-align: center;
    font-weight: 500;
}

.features {
    margin-bottom: 25px;
}

.features h3 {
    color: #333;
    font-size: 1.3em;
    margin-bottom: 15px;
    text-align: center;
}

.features ul {
    list-style: none;
    color: #666;
}

.features li {
    padding: 8px 0;
    font-size: 1em;
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer {
    text-align: center;
    color: #888;
    font-size: 0.9em;
    border-top: 1px solid #eee;
    padding-top: 20px;
}

.footer p {
    margin-bottom: 5px;
}

.security-note {
    font-weight: 600;
    color: #667eea;
}

/* Responsive Design */
@media (max-width: 600px) {
    .access-card {
        padding: 30px 25px;
        margin: 10px;
    }
    
    .logo h1 {
        font-size: 2em;
    }
    
    .access-form h2 {
        font-size: 1.5em;
    }
    
    .description {
        font-size: 1em;
    }
}

/* Animation for page load */
.access-card {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
