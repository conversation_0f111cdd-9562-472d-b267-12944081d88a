<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ShareFlix - Voice-Driven Investment Discovery</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Burger Menu -->
        <div class="burger-menu" id="burgerMenu">
            <div class="burger-icon">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <!-- Side Menu -->
        <div class="side-menu" id="sideMenu">
            <div class="side-menu-header">
                <h3>Menu</h3>
                <button class="close-menu" id="closeMenu">&times;</button>
            </div>
            <div class="side-menu-content">
                <button class="menu-item" id="menuHistoryBtn">
                    <i class="fas fa-history"></i>
                    <span>Conversation History</span>
                </button>
                <button class="menu-item" id="menuExportBtn">
                    <i class="fas fa-download"></i>
                    <span>Export Conversations</span>
                </button>
                <button class="menu-item" id="menuClearBtn">
                    <i class="fas fa-trash"></i>
                    <span>Clear History</span>
                </button>
            </div>
        </div>

        <!-- Menu Overlay -->
        <div class="menu-overlay" id="menuOverlay"></div>

        <!-- Progress Indicator -->
        <div class="progress-indicator" id="progressIndicator">
            <i class="fas fa-circle"></i> 2 Requests in progress
        </div>

        <header class="header">
            <h1><i class="fas fa-circle"></i> ShareFlix</h1>
            <p class="subtitle">Voice-Driven Investment Discovery Platform</p>
        </header>

        <main class="main-content">
            <!-- Voice Interface Section -->
            <div class="voice-interface">
                <div class="conversation-display" id="conversationDisplay">
                    <div class="welcome-message">
                        <i class="fas fa-robot"></i>
                        <p>Hello, James. The Market has been noisy - lets catch you up.</p>
                    </div>
                </div>

                <div class="voice-controls">
                    <div class="text-input-container">
                        <input type="text" class="text-input" id="textInput" placeholder="Chat with Echo">
                        <button class="send-button" id="sendButton">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>

                    <button class="mic-button" id="micButton">
                        <i class="fas fa-microphone" id="micIcon"></i>
                        <span class="mic-status" id="micStatus">Click to speak</span>
                    </button>

                    <button class="stop-button" id="stopButton" style="display: none;">
                        <i class="fas fa-stop"></i>
                        <span class="stop-text">Stop</span>
                    </button>
                </div>

                <!-- Conversation History Modal -->
                <div class="history-modal" id="historyModal" style="display: none;">
                    <div class="history-modal-content">
                        <div class="history-header">
                            <h2>Conversation History</h2>
                            <button class="close-history" id="closeHistory">&times;</button>
                        </div>
                        <div class="history-content" id="historyContent">
                            <div class="loading">Loading conversations...</div>
                        </div>
                        <div class="history-actions">
                            <button class="export-btn" id="exportHistory">Export History</button>
                            <button class="clear-btn" id="clearHistory">Clear History</button>
                        </div>
                    </div>

                    <div class="audio-controls">
                        <button class="control-btn" id="playButton" disabled>
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="control-btn" id="oldStopButton" disabled>
                            <i class="fas fa-stop"></i>
                        </button>
                    </div>
                </div>

                <!-- AI Thinking Indicator -->
                <div class="ai-thinking-indicator" id="aiThinkingIndicator" style="display: none;">
                    <div class="thinking-animation">
                        <div class="blobs">
                            <svg viewbox="0 0 1200 1200">
                                <g class="blob blob-1">
                                    <path d="M600,200 C800,200 1000,400 1000,600 C1000,800 800,1000 600,1000 C400,1000 200,800 200,600 C200,400 400,200 600,200 Z" />
                                </g>
                                <g class="blob blob-2">
                                    <path d="M600,150 C850,150 1050,350 1050,600 C1050,850 850,1050 600,1050 C350,1050 150,850 150,600 C150,350 350,150 600,150 Z" />
                                </g>
                                <g class="blob blob-3">
                                    <path d="M600,250 C750,250 950,450 950,600 C950,750 750,950 600,950 C450,950 250,750 250,600 C250,450 450,250 600,250 Z" />
                                </g>
                                <g class="blob blob-4">
                                    <path d="M600,300 C700,300 900,500 900,600 C900,700 700,900 600,900 C500,900 300,700 300,600 C300,500 500,300 600,300 Z" />
                                </g>
                                <g class="blob blob-1 alt">
                                    <path d="M600,200 C800,200 1000,400 1000,600 C1000,800 800,1000 600,1000 C400,1000 200,800 200,600 C200,400 400,200 600,200 Z" />
                                </g>
                                <g class="blob blob-2 alt">
                                    <path d="M600,150 C850,150 1050,350 1050,600 C1050,850 850,1050 600,1050 C350,1050 150,850 150,600 C150,350 350,150 600,150 Z" />
                                </g>
                                <g class="blob blob-3 alt">
                                    <path d="M600,250 C750,250 950,450 950,600 C950,750 750,950 600,950 C450,950 250,750 250,600 C250,450 450,250 600,250 Z" />
                                </g>
                                <g class="blob blob-4 alt">
                                    <path d="M600,300 C700,300 900,500 900,600 C900,700 700,900 600,900 C500,900 300,700 300,600 C300,500 500,300 600,300 Z" />
                                </g>
                            </svg>
                        </div>
                    </div>
                    <span class="thinking-text">Sarah is thinking...</span>
                </div>



                <div class="status-indicator" id="statusIndicator">
                    <span class="status-text">Start typing or speaking...</span>
                </div>

                <!-- Stock Preview Card -->
                <div class="stock-preview-card" id="stockPreviewCard" style="display: none;">
                    <div class="stock-preview-content">
                        <div class="stock-header">
                            <span class="company-name" id="previewCompanyName">Apple Inc.</span>
                            <span class="ticker-symbol" id="previewTicker">AAPL</span>
                        </div>
                        <div class="stock-metrics">
                            <div class="price-info">
                                <span class="current-price" id="previewPrice">$189.84</span>
                                <span class="price-change" id="previewChange">+2.34%</span>
                            </div>
                            <div class="additional-info">
                                <span class="market-cap" id="previewMarketCap">$2.89T</span>
                                <span class="price-target" id="previewTarget">PT: $220</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Lead Information Panel -->
            <div class="lead-panel" id="leadPanel">
                <h3><i class="fas fa-user-plus"></i> Lead Information</h3>
                <div class="lead-info" id="leadInfo">
                    <p class="info-placeholder">Lead information will appear here as the conversation progresses...</p>
                </div>
                
                <div class="lead-actions">
                    <button class="action-btn save-btn" id="saveLeadBtn" disabled>
                        <i class="fas fa-save"></i> Save Lead
                    </button>
                    <button class="action-btn export-btn" id="exportLeadBtn" disabled>
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2024 ShareFlix. Powered by OpenAI.</p>
        </footer>
    </div>

    <!-- Audio element for playback -->
    <audio id="audioPlayer" controls style="display: none;"></audio>

    <script src="app.js"></script>
</body>
</html>
