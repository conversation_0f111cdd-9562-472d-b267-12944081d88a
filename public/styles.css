/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    background: #FFFFFF;
    min-height: 100vh;
    color: #000000;
    overflow-x: hidden;
}

.container {
    max-width: 400px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #FFFFFF;
    position: relative;
}

/* Header */
.header {
    padding: 60px 20px 20px 20px;
    text-align: left;
}

.header h1 {
    font-weight: 600;
    color: #000000;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.header h1 i {
    color: #FFEE8C;
    font-size: 1rem;
}

.subtitle {
    font-size: 0.9rem;
    color: #888;
    display: none; /* Hide subtitle in mobile design */
}

/* Main content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 20px;
}

/* Burger Menu */
.burger-menu {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.burger-menu:hover {
    background: rgba(255, 255, 255, 0.9);
}

.burger-icon {
    width: 24px;
    height: 18px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.burger-icon span {
    width: 100%;
    height: 2px;
    background: #000000;
    border-radius: 1px;
    transition: all 0.3s ease;
}

.burger-menu.active .burger-icon span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.burger-menu.active .burger-icon span:nth-child(2) {
    opacity: 0;
}

.burger-menu.active .burger-icon span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Side Menu */
.side-menu {
    position: fixed;
    top: 0;
    left: -300px;
    width: 300px;
    height: 100vh;
    background: #FFFFFF;
    z-index: 1001;
    transition: left 0.3s ease;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
}

.side-menu.active {
    left: 0;
}

.side-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #333;
}

.side-menu-header h3 {
    color: #000000;
    margin: 0;
    font-size: 1.3rem;
}

.close-menu {
    background: none;
    border: none;
    color: #000000;
    font-size: 1.8rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-menu:hover {
    background: #333;
}

.side-menu-content {
    padding: 20px 0;
}

.menu-item {
    width: 100%;
    padding: 15px 25px;
    background: none;
    border: none;
    color: #000000;
    text-align: left;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 1rem;
    transition: background 0.3s ease;
}

.menu-item:hover {
    background: #f0f0f0;
}

.menu-item i {
    width: 20px;
    color: #FFEE8C;
}

.menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Voice Interface */
.voice-interface {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: transparent;
}

.conversation-display {
    flex: 1;
    overflow-y: auto;
    padding: 20px 0;
    background: transparent;
    margin-bottom: 20px;
}

.welcome-message {
    margin-bottom: 30px;
}

.welcome-message p {
    font-weight: 600;
    color: #000000;
    line-height: 1.3;
    margin-bottom: 15px;
    font-size: 14px;
}

.welcome-message i {
    display: none;
}

.message {
    margin-bottom: 20px;
    line-height: 1.4;
    word-wrap: break-word;
    font-size: 14px;
}

.user-message {
    color: #000000;
    text-align: left;
    font-weight: 500;
    font-size: 14px;
}

.ai-message {
    color: #000000;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 30px;
    font-size: 14px;
}

.play-audio-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #FFEE8C;
    color: #000000;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    margin-top: 10px;
    transition: all 0.3s ease;
}

.play-audio-btn:hover {
    background: #FFE066;
    transform: scale(1.05);
}

.message-content {
    display: flex;
    flex-direction: column;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    margin: 15px auto;
    max-width: 90%;
}

.error-content strong {
    display: block;
    margin-bottom: 8px;
    font-size: 1.1em;
}

.error-content details {
    margin-top: 10px;
    padding: 8px;
    background: rgba(0,0,0,0.1);
    border-radius: 4px;
}

.error-content summary {
    cursor: pointer;
    font-weight: 500;
    margin-bottom: 5px;
}

.error-content pre {
    font-size: 0.85em;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 5px 0 0 0;
}

/* Voice Controls */
.voice-controls {
    position: fixed;
    bottom: 39px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    z-index: 1001;
}

.text-input-container {
    display: flex;
    align-items: center;
    background: #ffffff;
    border-radius: 25px;
    padding: 8px 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border: 1px solid #e0e0e0;
    min-width: 300px;
}

.text-input {
    flex: 1;
    border: none;
    outline: none;
    padding: 8px 12px;
    font-size: 14px;
    background: transparent;
    color: #333;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.text-input::placeholder {
    color: #999;
}

.send-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    background: #333;
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
}

.send-button:hover {
    background: #444;
    transform: scale(1.05);
}

.send-button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.mic-button {
    width: 73px;
    height: 73px;
    border-radius: 50%;
    border: none;
    background: #333;
    color: #ffffff;
    font-size: 1.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.mic-button:hover {
    background: #444;
    transform: scale(1.05);
}

.mic-button.recording {
    background: #FFEE8C;
    animation: pulse 1.5s infinite;
}

.mic-button.listening {
    background: #FFE066;
    animation: listening-pulse 2s infinite;
}

.mic-button:disabled {
    background: #666;
    cursor: not-allowed;
    opacity: 0.6;
    transform: none !important;
}

.mic-button:disabled:hover {
    background: #666;
    transform: none;
}

.mic-status {
    display: none; /* Hide status text in mobile design */
}



@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes listening-pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(0,0,0,0.3), 0 0 0 0 rgba(0, 122, 204, 0.7);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0,0,0,0.3), 0 0 0 10px rgba(0, 122, 204, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(0,0,0,0.3), 0 0 0 0 rgba(0, 122, 204, 0);
    }
}



/* Stop Button */
.stop-button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    background: #ff4757;
    color: #ffffff;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
}

.stop-button:hover {
    background: #ff3742;
    transform: scale(1.05);
}

.stop-text {
    font-size: 0.7rem;
    margin-top: 2px;
    font-weight: 500;
}



.audio-controls {
    display: none; /* Hide audio controls in mobile design */
}

.control-btn {
    display: none;
}

/* Status Indicator */
.status-indicator {
    position: fixed;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    padding: 8px 16px;
    border-radius: 20px;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    max-width: 280px;
    opacity: 1;
}

.status-indicator.hidden {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
    pointer-events: none;
}

.status-text {
    font-size: 0.8rem;
    color: #FFEE8C;
    font-weight: 500;
}

/* Lead Panel - Hidden in mobile design */
.lead-panel {
    display: none;
}

/* Footer - Hidden in mobile design */
.footer {
    display: none;
}

/* Progress indicator */
.progress-indicator {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255, 238, 140, 0.2);
    color: #FFEE8C;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    display: none;
}

.progress-indicator.show {
    display: block;
}

/* Responsive Typography - Mobile First */

/* Base mobile styles (320px - 480px) */
.welcome-message p {
    font-size: 14px; /* Set to 14px as requested */
}

.ai-message {
    font-size: 14px; /* Set to 14px as requested */
}

.message {
    font-size: 14px; /* Set to 14px as requested */
}

.header h1 {
    font-size: 1rem; /* Reduced from 1.1rem for small screens */
}

/* Small mobile screens (up to 375px) */
@media (max-width: 375px) {
    .welcome-message p {
        font-size: 14px;
        line-height: 1.2;
    }

    .ai-message {
        font-size: 14px;
        line-height: 1.2;
        margin-bottom: 25px;
    }

    .message {
        font-size: 14px;
        line-height: 1.3;
    }

    .header h1 {
        font-size: 0.95rem;
    }

    .subtitle {
        font-size: 0.8rem;
    }

    .status-text {
        font-size: 0.75rem;
    }

    .container {
        padding: 0 15px;
    }
}

/* Medium mobile screens (376px - 480px) */
@media (min-width: 376px) and (max-width: 480px) {
    .welcome-message p {
        font-size: 14px;
    }

    .ai-message {
        font-size: 14px;
    }

    .message {
        font-size: 14px;
    }
}

/* Large mobile screens (481px - 767px) */
@media (min-width: 481px) and (max-width: 767px) {
    .welcome-message p {
        font-size: 14px;
    }

    .ai-message {
        font-size: 14px;
    }

    .message {
        font-size: 14px;
    }

    .header h1 {
        font-size: 1.1rem;
    }

    .container {
        max-width: 450px;
    }
}

/* Tablet and larger screens (768px+) */
@media (min-width: 768px) {
    .container {
        max-width: 500px;
        padding: 0 30px;
    }

    .welcome-message p {
        font-size: 14px;
        line-height: 1.3;
    }

    .ai-message {
        font-size: 14px;
        line-height: 1.3;
        margin-bottom: 35px;
    }

    .message {
        font-size: 14px;
        line-height: 1.4;
    }

    .header h1 {
        font-size: 1.2rem;
    }

    .subtitle {
        display: block; /* Show subtitle on larger screens */
        font-size: 1rem;
    }

    .mic-status {
        display: block; /* Show mic status on larger screens */
        font-size: 0.9rem;
        color: #888;
        margin-top: 5px;
    }

    .status-text {
        font-size: 0.9rem;
    }

    .conversation-display {
        padding: 30px 0;
    }
}

/* Large screens (1024px+) */
@media (min-width: 1024px) {
    .container {
        max-width: 600px;
    }

    .welcome-message p {
        font-size: 14px;
    }

    .ai-message {
        font-size: 14px;
    }

    .message {
        font-size: 14px;
    }
}

/* AI Thinking Animation */
.ai-thinking-indicator {
    position: fixed;
    bottom: 39px;
    left: 129px;
    z-index: 1000;
    display: block;
    padding: 0;
    margin: 0;
    background: transparent;
    border: none;
    animation: fadeIn 0.3s ease-in-out;
    width: 73px;
    height: 73px;
    filter: drop-shadow(0 0 20px rgba(255, 238, 140, 0.3));
}

/* Alternative: Bottom Left Corner Position */
.ai-thinking-indicator.bottom-left {
    bottom: 30px;
    left: 30px;
    transform: none;
}

.thinking-animation {
    width: 73px;
    height: 73px;
    position: relative;
}

.thinking-animation .blobs {
    width: 100%;
    height: 100%;
}

.thinking-animation svg {
    position: relative;
    height: 100%;
    z-index: 2;
}

.thinking-animation .blob {
    animation: rotate 25s infinite alternate ease-in-out;
    transform-origin: 50% 50%;
    opacity: 0.7;
}

.thinking-animation .blob path {
    animation: blob-anim-1 5s infinite alternate cubic-bezier(0.45, 0.2, 0.55, 0.8);
    transform-origin: 50% 50%;
    transform: scale(0.8);
    transition: fill 800ms ease;
}

.thinking-animation .blob.alt {
    animation-direction: alternate-reverse;
    opacity: 0.3;
}

.thinking-animation .blob-1 path {
    fill: #FFEE8C;
    filter: blur(1rem);
}

.thinking-animation .blob-2 {
    animation-duration: 18s;
    animation-direction: alternate-reverse;
}

.thinking-animation .blob-2 path {
    fill: #4344ad;
    animation-name: blob-anim-2;
    animation-duration: 7s;
    filter: blur(0.75rem);
    transform: scale(0.78);
}

.thinking-animation .blob-2.alt {
    animation-direction: alternate;
}

.thinking-animation .blob-3 {
    animation-duration: 23s;
}

.thinking-animation .blob-3 path {
    fill: #74d9e1;
    animation-name: blob-anim-3;
    animation-duration: 6s;
    filter: blur(0.5rem);
    transform: scale(0.76);
}

.thinking-animation .blob-4 {
    animation-duration: 31s;
    animation-direction: alternate-reverse;
    opacity: 0.9;
}

.thinking-animation .blob-4 path {
    fill: #1a1a1a;
    animation-name: blob-anim-4;
    animation-duration: 10s;
    filter: blur(10rem);
    transform: scale(0.5);
}

.thinking-animation .blob-4.alt {
    animation-direction: alternate;
    opacity: 0.8;
}

.thinking-text {
    display: none; /* Hide text to match reference design */
    color: #FFEE8C;
    font-size: 1.1rem;
    font-weight: 500;
    text-align: center;
    opacity: 0.9;
    transition: color 0.3s ease;
}

/* Enhanced state variations */
.ai-thinking-indicator[data-state="thinking"] {
    filter: drop-shadow(0 0 20px rgba(255, 238, 140, 0.4));
}

.ai-thinking-indicator[data-state="speaking"] {
    filter: drop-shadow(0 0 25px rgba(255, 230, 102, 0.5));
    animation: speaking-pulse 1.5s infinite ease-in-out;
}

.ai-thinking-indicator[data-state="processing"] {
    filter: drop-shadow(0 0 30px rgba(255, 238, 140, 0.6));
    animation: processing-spin 3s infinite linear;
}

.ai-thinking-indicator[data-state="speaking"] .thinking-text {
    color: #FFE066;
}

.ai-thinking-indicator[data-state="speaking"] .blob-1 path {
    fill: #FFE066;
    animation-duration: 3s;
}

.ai-thinking-indicator[data-state="speaking"] .blob-2 path {
    fill: #FFEE8C;
    animation-duration: 4s;
}

.ai-thinking-indicator[data-state="speaking"] .blob-3 path {
    fill: #000000;
    animation-duration: 2.5s;
}

.ai-thinking-indicator[data-state="processing"] .blob-1 path {
    fill: #FFE066;
    animation-duration: 2s;
}

.ai-thinking-indicator[data-state="processing"] .blob-2 path {
    fill: #FFEE8C;
    animation-duration: 2.5s;
}

.ai-thinking-indicator[data-state="processing"] .blob-3 path {
    fill: #FFD700;
    animation-duration: 1.8s;
}

@keyframes speaking-pulse {
    0%, 100% {
        transform: scale(1);
        filter: drop-shadow(0 0 25px rgba(255, 230, 102, 0.5));
    }
    50% {
        transform: scale(1.1);
        filter: drop-shadow(0 0 35px rgba(255, 230, 102, 0.8));
    }
}

@keyframes processing-spin {
    0% {
        transform: rotate(0deg);
        filter: drop-shadow(0 0 30px rgba(255, 238, 140, 0.6));
    }
    50% {
        filter: drop-shadow(0 0 40px rgba(255, 238, 140, 0.8));
    }
    100% {
        transform: rotate(360deg);
        filter: drop-shadow(0 0 30px rgba(255, 238, 140, 0.6));
    }
}

/* Blob Animation Keyframes */
@keyframes blob-anim-1 {
    0% {
        d: path("M 100 600 q 0 -500, 500 -500 t 500 500 t -500 500 T 100 600 z");
    }
    30% {
        d: path("M 100 600 q -50 -400, 500 -500 t 450 550 t -500 500 T 100 600 z");
    }
    70% {
        d: path("M 100 600 q 0 -400, 500 -500 t 400 500 t -500 500 T 100 600 z");
    }
    100% {
        d: path("M 150 600 q 0 -600, 500 -500 t 500 550 t -500 500 T 150 600 z");
    }
}

@keyframes blob-anim-2 {
    0% {
        d: path("M 100 600 q 0 -400, 500 -500 t 400 500 t -500 500 T 100 600 z");
    }
    40% {
        d: path("M 150 600 q 0 -600, 500 -500 t 500 550 t -500 500 T 150 600 z");
    }
    80% {
        d: path("M 100 600 q -50 -400, 500 -500 t 450 550 t -500 500 T 100 600 z");
    }
    100% {
        d: path("M 100 600 q 100 -600, 500 -500 t 400 500 t -500 500 T 100 600 z");
    }
}

@keyframes blob-anim-3 {
    0% {
        d: path("M 100 600 q -50 -400, 500 -500 t 450 550 t -500 500 T 100 600 z");
    }
    35% {
        d: path("M 150 600 q 0 -600, 500 -500 t 500 550 t -500 500 T 150 600 z");
    }
    75% {
        d: path("M 100 600 q 100 -600, 500 -500 t 400 500 t -500 500 T 100 600 z");
    }
    100% {
        d: path("M 100 600 q 0 -400, 500 -500 t 400 500 t -500 500 T 100 600 z");
    }
}

@keyframes blob-anim-4 {
    0% {
        d: path("M 150 600 q 0 -600, 500 -500 t 500 550 t -500 500 T 150 600 z");
    }
    30% {
        d: path("M 100 600 q 100 -600, 500 -500 t 400 500 t -500 500 T 100 600 z");
    }
    70% {
        d: path("M 100 600 q -50 -400, 500 -500 t 450 550 t -500 500 T 100 600 z");
    }
    100% {
        d: path("M 150 600 q 0 -600, 500 -500 t 500 550 t -500 500 T 150 600 z");
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* Responsive adjustments for thinking animation */
@media (max-width: 480px) {
    .ai-thinking-indicator {
        padding: 20px;
    }

    .thinking-animation {
        width: 100px;
        height: 100px;
    }

    .thinking-text {
        font-size: 1rem;
    }
}

/* History Modal Styles */
.history-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-in-out;
}

.history-modal-content {
    background: #FFFFFF;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #333;
}

.history-header h2 {
    color: #000000;
    margin: 0;
    font-size: 1.5rem;
}

.close-history {
    background: none;
    border: none;
    color: #000000;
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-history:hover {
    background: #333;
}

.history-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px 25px;
    max-height: 50vh;
}

/* Grouped Conversation Styles */
.conversation-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.conversation-group {
    background: #2a2a2a;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #333;
    transition: all 0.3s ease;
}

.conversation-group:hover {
    border-color: #FFEE8C;
}

.conversation-summary {
    padding: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: background 0.3s ease;
}

.conversation-summary:hover {
    background: #333;
}

.conversation-header {
    flex: 1;
}

.conversation-title {
    color: #000000;
    margin: 0 0 8px 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.conversation-meta {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 8px;
}

.conversation-date {
    color: #888;
    font-size: 0.9rem;
}

.message-count {
    color: #FFEE8C;
    font-size: 0.85rem;
    background: rgba(255, 238, 140, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
}

.conversation-preview {
    color: #ccc;
    font-size: 0.9rem;
    line-height: 1.4;
}

.expand-icon {
    color: #888;
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.conversation-details {
    border-top: 1px solid #333;
    background: #1e1e1e;
}

.message-list {
    padding: 0;
}

.message-exchange {
    padding: 15px 20px;
    border-bottom: 1px solid #333;
}

.message-exchange:last-child {
    border-bottom: none;
}

.message-time {
    color: #666;
    font-size: 0.8rem;
    margin-bottom: 8px;
}

.user-message {
    color: #000000;
    margin-bottom: 10px;
    padding: 8px 12px;
    background: #f0f0f0;
    border-radius: 8px;
    border-left: 3px solid #FFEE8C;
}

.ai-message {
    color: #000000;
    padding: 8px 12px;
    background: rgba(255, 238, 140, 0.05);
    border-radius: 8px;
    border-left: 3px solid #FFEE8C;
    line-height: 1.4;
}

.conversation-actions {
    padding: 15px 20px;
    border-top: 1px solid #333;
    background: #1a1a1a;
    display: flex;
    justify-content: center;
}

.continue-btn {
    background: #FFEE8C;
    color: #000;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.continue-btn:hover {
    background: #FFE066;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 238, 140, 0.3);
}

.continue-btn i {
    font-size: 0.9rem;
}

.system-message {
    background: rgba(136, 136, 136, 0.1);
    border: 1px solid #444;
    border-radius: 8px;
    padding: 10px 15px;
    margin: 15px 0;
    text-align: center;
    color: #888;
    font-style: italic;
}

.history-actions {
    display: flex;
    gap: 15px;
    padding: 20px 25px;
    border-top: 1px solid #333;
    justify-content: flex-end;
}

.export-btn, .clear-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.export-btn {
    background: #FFEE8C;
    color: #000;
}

.export-btn:hover {
    background: #FFE066;
}

.clear-btn {
    background: #e74c3c;
    color: #fff;
}

.clear-btn:hover {
    background: #c0392b;
}

.loading {
    text-align: center;
    color: #888;
    padding: 40px;
}

.no-history {
    text-align: center;
    color: #888;
    padding: 40px;
}

/* Stock Carousel Styles */
.stock-carousel-container {
    margin: 12px 0 8px 0;
    animation: slideInFromBottom 0.4s ease-out;
    position: relative;
}

.stock-carousel-wrapper {
    overflow: hidden;
    border-radius: 12px;
    height: 520px; /* Fixed height for carousel */
}

.stock-carousel-track {
    display: flex;
    transition: transform 0.3s ease;
    height: 100%;
    cursor: grab;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.stock-carousel-track:active {
    cursor: grabbing;
}

.carousel-card {
    flex: 0 0 100%;
    height: 100%;
    margin: 0 !important;
}

.carousel-card .inline-stock-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.carousel-dots {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 12px;
}

.carousel-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: none;
    background: #ccc;
    cursor: pointer;
    transition: all 0.3s ease;
}

.carousel-dot.active {
    background: #333;
    transform: scale(1.2);
}

.carousel-dot:hover {
    background: #666;
}

/* Hide dots and auto-advance for single stock */
.stock-carousel-container[data-total-slides="1"] .carousel-dots {
    display: none;
}

.stock-carousel-container[data-total-slides="1"] .stock-carousel-track {
    cursor: default;
}

/* Inline Stock Card - Part of conversation flow */
.inline-stock-card {
    margin: 12px 0 8px 0;
    animation: slideInFromBottom 0.4s ease-out;
}

.inline-stock-content {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-width: 450px;
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    transition: all 0.3s ease;
}

.inline-stock-content:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

@keyframes slideInFromBottom {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stock Preview Card - Legacy floating card (keeping for compatibility) */
.stock-preview-card {
    position: fixed;
    bottom: 120px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 999;
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
}

.stock-preview-card.show {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

.stock-preview-content {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 238, 140, 0.2);
    border-radius: 12px;
    padding: 14px 18px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    min-width: 200px;
    text-align: center;
}

/* Enhanced Stock Card Styles */
.inline-stock-content .stock-header {
    margin-bottom: 20px;
}

.inline-stock-content .company-name {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    display: block;
    margin-bottom: 6px;
    line-height: 1.2;
    max-width: none;
    overflow: visible;
    text-overflow: unset;
    white-space: normal;
}

.inline-stock-content .ticker-symbol {
    font-size: 16px;
    color: #666;
    font-weight: 500;
    text-transform: uppercase;
}

.community-insight {
    margin-bottom: 24px;
}

.profile-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.profile-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #f0f0f0;
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.member-name {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.price-section {
    margin-bottom: 20px;
}

.price-label {
    font-size: 16px;
    color: #666;
    font-weight: 600;
    display: block;
    margin-bottom: 8px;
}

.inline-stock-content .price-info {
    display: flex;
    align-items: baseline;
    gap: 12px;
}

.inline-stock-content .current-price {
    font-size: 36px;
    font-weight: 700;
    color: #333;
}

.inline-stock-content .price-change {
    font-size: 18px;
    font-weight: 600;
}

.inline-stock-content .price-change.positive {
    color: #22c55e;
}

.inline-stock-content .price-change.negative {
    color: #ef4444;
}

.metrics-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    gap: 20px;
}

.metric-item {
    flex: 1;
}

.metric-label {
    font-size: 14px;
    color: #666;
    font-weight: 600;
    display: block;
    margin-bottom: 4px;
}

.metric-value {
    font-size: 18px;
    color: #333;
    font-weight: 700;
}

.target-upside {
    color: #22c55e;
    font-size: 14px;
    font-weight: 600;
}

.thesis-section {
    margin-bottom: 20px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
}

.thesis-label {
    font-size: 16px;
    color: #333;
    font-weight: 700;
    display: block;
    margin-bottom: 8px;
}

.thesis-text {
    font-size: 14px;
    color: #555;
    line-height: 1.5;
    margin: 0;
}

.timestamp {
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;
}

.pricing-note {
    font-size: 12px;
    color: #999;
    font-style: italic;
}

/* Legacy styles for floating stock cards */
.stock-header {
    display: flex;
    flex-direction: column;
    gap: 2px;
    align-items: center;
    margin-bottom: 8px;
}

.company-name {
    font-size: 0.85rem;
    font-weight: 500;
    color: #000000;
    line-height: 1.2;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ticker-symbol, .inline-stock-content .ticker-symbol {
    font-size: 0.75rem;
    font-weight: 600;
    color: #FFEE8C;
    letter-spacing: 0.5px;
    opacity: 0.9;
}

.stock-metrics, .inline-stock-content .stock-metrics {
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: center;
}

/* Shared styling for both inline and floating stock cards */
.price-info, .inline-stock-content .price-info {
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
}

.current-price, .inline-stock-content .current-price {
    font-size: 0.9rem;
    font-weight: 600;
    color: #000000;
}

.price-change, .inline-stock-content .price-change {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 4px;
    line-height: 1;
}

.price-change.positive, .inline-stock-content .price-change.positive {
    color: #FFEE8C;
    background: rgba(255, 238, 140, 0.15);
}

.price-change.negative, .inline-stock-content .price-change.negative {
    color: #ff6b6b;
    background: rgba(255, 107, 107, 0.15);
}

.additional-info, .inline-stock-content .additional-info {
    display: flex;
    align-items: center;
    gap: 12px;
    justify-content: center;
    font-size: 0.7rem;
    color: #888;
}

.market-cap, .price-target, .inline-stock-content .market-cap, .inline-stock-content .price-target {
    font-weight: 500;
}

/* Animation for stock preview card */
@keyframes slideUpFade {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes slideDownFade {
    0% {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
}

.stock-preview-card.animate-in {
    animation: slideUpFade 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.stock-preview-card.animate-out {
    animation: slideDownFade 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Responsive adjustments for inline stock cards */
@media (max-width: 375px) {
    .inline-stock-content {
        padding: 12px 14px;
        max-width: 240px;
    }

    .inline-stock-content .company-name {
        font-size: 0.8rem;
        max-width: 160px;
    }

    .inline-stock-content .ticker-symbol {
        font-size: 0.7rem;
    }

    .inline-stock-content .current-price {
        font-size: 0.85rem;
    }

    .inline-stock-content .price-change {
        font-size: 0.7rem;
    }

    .inline-stock-content .additional-info {
        font-size: 0.65rem;
        gap: 8px;
    }
}

/* Responsive adjustments for floating stock preview card */
@media (max-width: 375px) {
    .stock-preview-card {
        bottom: 110px;
    }

    .stock-preview-content {
        padding: 12px 14px;
        min-width: 180px;
    }

    .company-name {
        font-size: 0.8rem;
        max-width: 160px;
    }

    .ticker-symbol {
        font-size: 0.7rem;
    }

    .current-price {
        font-size: 0.85rem;
    }

    .price-change {
        font-size: 0.7rem;
    }

    .additional-info {
        font-size: 0.65rem;
        gap: 8px;
    }
}

/* Responsive carousel adjustments */
@media (max-width: 480px) {
    .stock-carousel-wrapper {
        height: 480px;
    }

    .carousel-card .inline-stock-content {
        padding: 20px;
    }
}

@media (min-width: 768px) {
    .stock-carousel-wrapper {
        height: 550px;
    }

    .carousel-card .inline-stock-content {
        padding: 28px;
        max-width: none;
    }

    .inline-stock-content {
        padding: 18px 22px;
        max-width: 320px;
    }

    .inline-stock-content .company-name {
        font-size: 0.9rem;
        max-width: 200px;
    }

    .inline-stock-content .ticker-symbol {
        font-size: 0.8rem;
    }

    .inline-stock-content .current-price {
        font-size: 1rem;
    }

    .inline-stock-content .price-change {
        font-size: 0.8rem;
    }

    .inline-stock-content .additional-info {
        font-size: 0.75rem;
        gap: 16px;
    }

    /* Floating stock preview card for larger screens */
    .stock-preview-card {
        bottom: 140px;
    }

    .stock-preview-content {
        padding: 16px 20px;
        min-width: 220px;
    }

    .company-name {
        font-size: 0.9rem;
        max-width: 200px;
    }

    .ticker-symbol {
        font-size: 0.8rem;
    }

    .current-price {
        font-size: 1rem;
    }

    .price-change {
        font-size: 0.8rem;
    }

    .additional-info {
        font-size: 0.75rem;
        gap: 16px;
    }
}
