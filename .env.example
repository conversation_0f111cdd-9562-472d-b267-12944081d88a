# OpenAI API Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Claude API Configuration (Fallback)
CLAUDE_API_KEY=your-claude-api-key-here

# Server Configuration
PORT=3000
NODE_ENV=production

# Security Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-minimum-32-characters-long
ENCRYPTION_KEY=your-64-character-hex-encryption-key-here

# Access Control
ACCESS_PASSWORD=echo2025
ACCESS_SESSION_TIMEOUT=86400000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Security
MAX_FILE_SIZE=5242880

# CORS Configuration
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# AI Provider Settings
PRIMARY_AI_PROVIDER=openai
ENABLE_AI_FALLBACK=true

# Mock Mode (set to 'true' to test without API calls)
MOCK_MODE=false

# Security Headers
HSTS_MAX_AGE=31536000

# Session Configuration
SESSION_TIMEOUT=86400
