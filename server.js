require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const cookieParser = require('cookie-parser');
const path = require('path');
const fs = require('fs');

// Import services and middleware
const AIService = require('./services/aiService');
const { detectStocksInText, getStockInfo } = require('./services/stockDetectionService');
const {
  STOCK_BUSINESS_SYSTEM_PROMPT,
  CONVERSATION_STARTERS,
  MARKET_INSIGHTS,
} = require('./prompts');
const { authService, optionalAuth } = require('./middleware/auth');
const {
  validateMessage,
  validateLeadData,
  validateConversationHistory,
  validateUserRegistration,
  validateUserLogin,
  validateTextToSpeech,
  handleValidationErrors,
} = require('./middleware/validation');

// Writable data directory (Heroku: use /tmp)
const DATA_DIR = process.env.DATA_DIR || path.join(__dirname, 'data');
if (!fs.existsSync(DATA_DIR)) {
  try {
    fs.mkdirSync(DATA_DIR, { recursive: true });
  } catch (e) {
    /* ignore */
  }
}

const {
  encryptionService,
  encryptConversation,
  decryptConversation,
  anonymizeForLogging,
} = require('./services/encryption');
const {
  upload,
  additionalSecurityChecks,
  secureDelete,
} = require('./middleware/fileUpload');
const {
  accessControlService,
  requireAccessPassword,
  validateAccessPasswordInput,
} = require('./middleware/accessControl');

// Full context conversation loading system (GPT-4o 128k context)

/**
 * Parse timestamp to Date object for sorting
 */
function parseTimestamp(timestamp) {
  try {
    // Handle different timestamp formats
    if (timestamp.includes('/')) {
      // Format: "03/07/2025, 08:26:35" or "04/08/2025, 12:17:44"
      const [datePart, timePart] = timestamp.split(', ');
      const [day, month, year] = datePart.split('/');
      const [hour, minute, second] = timePart.split(':');
      return new Date(year, month - 1, day, hour, minute, second);
    } else {
      // ISO format or other formats
      return new Date(timestamp);
    }
  } catch (error) {
    console.warn('Could not parse timestamp:', timestamp);
    return new Date(0); // Return epoch if parsing fails
  }
}

/**
 * Load all conversation files and combine them chronologically
 */
function loadAllConversations() {
  const allConversations = [];
  const conversationFiles = [
    './conversations/july_collectivechat_data.json',
    './conversations/Collective Chat.json',
    './conversations/Curation Collective.json',
  ];

  let totalLoaded = 0;

  conversationFiles.forEach((filePath) => {
    try {
      const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      if (Array.isArray(data)) {
        // Add source file info to each conversation
        const conversationsWithSource = data.map((conv) => ({
          ...conv,
          sourceFile: filePath.replace('./', '').replace('.json', ''),
        }));
        allConversations.push(...conversationsWithSource);
        totalLoaded += data.length;
        console.log(`✅ Loaded ${data.length} conversations from ${filePath}`);
      }
    } catch (error) {
      console.log(`⚠️ Could not load ${filePath}:`, error.message);
    }
  });

  // Sort all conversations chronologically
  allConversations.sort((a, b) => {
    const dateA = parseTimestamp(a.timestamp);
    const dateB = parseTimestamp(b.timestamp);
    return dateA - dateB;
  });

  console.log(`🎯 Total conversations loaded: ${totalLoaded}`);
  console.log(
    `📅 Date range: ${allConversations[0]?.timestamp} to ${
      allConversations[allConversations.length - 1]?.timestamp
    }`
  );

  return allConversations;
}

/**
 * Extract keywords from user message for smart loading
 */
function extractKeywords(userMessage) {
  const message = userMessage.toLowerCase();

  // All users from conversation data (extracted from chat files)
  const people = [
    'adam calman',
    'adam clib',
    'al russell',
    'alistair court',
    'andrew cawker',
    'andrew draycott',
    'ben finegold',
    'ben funnell',
    'calum',
    'charlie morris',
    'chris huggins',
    'chris nyers',
    'conor moylan',
    'danny solomon',
    'david haysey',
    'dipesh patel',
    'dominic collins',
    'ed farquhar',
    'eddie benson',
    'gerard barron',
    'gerhard',
    'giles fitzpatrick',
    'gr',
    'greg smith',
    'henry',
    'hope finegold',
    'jacco reijtenbagh',
    'jacko',
    'jason mackay',
    'jb',
    'jerry de leeuw',
    'jim',
    'john drinkwater',
    'jonnie goodwin',
    'jonny creagh coen',
    'jonny haycock',
    'josh clem',
    'julie',
    'julius',
    'katie jackson',
    'kc shetty',
    'lee shave',
    'luke dyas',
    'mark',
    'mark astley',
    'mark faulkner',
    'mark wharrier',
    'matt h',
    'matt jonns',
    'mezza',
    'michael alen-buckley',
    'morgan',
    'morlene fisher',
    'nick finegold',
    'nirav chum',
    'noam gottesman',
    'paul dewinter',
    'peter cowan',
    'philip donald',
    'raj shah',
    'rich mcdonald',
    'richard windsor',
    'robin maxwell',
    'simon brewer',
    'simon says',
    'sscook',
    'stretch',
    'tagg mcgurrin',
    'tfg',
    'thorsten-a. weigle',
    'tim sykes',
    'tom wiggin',
    'wan danger',
    'will meadon',
    'william nutting',
    'william russell',
    'yakov schleider',
    'yash gaikwad uk',
    'zac bobolakis',
  ];

  // Company names and stock symbols
  const companies = [
    'apple',
    'aapl',
    'tesla',
    'tsla',
    'spotify',
    'spot',
    'bp',
    'coinbase',
    'coin',
    'meta',
    'palantir',
    'pltr',
    'serica',
    'qualcomm',
    'qcom',
    'nvidia',
    'nvda',
    'microsoft',
    'msft',
    'amazon',
    'amzn',
    'google',
    'googl',
    'netflix',
    'nflx',
  ];

  // Investment terms
  const investmentTerms = [
    'stock',
    'investment',
    'portfolio',
    'valuation',
    'earnings',
    'dividend',
    'market',
    'trading',
    'analysis',
    'catalyst',
    'price target',
    'revenue',
    'growth',
    'sector',
    'etf',
    'options',
    'futures',
  ];

  // Market topics
  const topics = [
    'energy transition',
    'ai',
    'nuclear',
    'geothermal',
    'hydrogen',
    'crypto',
    'ethereum',
    'bitcoin',
    'defense',
    'healthcare',
    'technology',
    'oil',
    'gas',
    'renewable',
  ];

  const foundKeywords = {
    people: people.filter((person) => message.includes(person)),
    companies: companies.filter((company) => message.includes(company)),
    investmentTerms: investmentTerms.filter((term) => message.includes(term)),
    topics: topics.filter((topic) => message.includes(topic)),
  };

  return foundKeywords;
}

/**
 * Calculate relevance score for a conversation based on keywords
 */
function calculateRelevanceScore(conversation, keywords) {
  let score = 0;
  const content = (
    conversation.content ||
    conversation.message ||
    ''
  ).toLowerCase();
  const user = (
    conversation.user ||
    conversation.author ||
    conversation.sender ||
    ''
  ).toLowerCase();

  // High weight for people matches
  keywords.people.forEach((person) => {
    if (user.includes(person) || content.includes(person)) {
      score += 10;
    }
  });

  // Medium weight for company matches
  keywords.companies.forEach((company) => {
    if (content.includes(company)) {
      score += 5;
    }
  });

  // Lower weight for general terms
  keywords.investmentTerms.forEach((term) => {
    if (content.includes(term)) {
      score += 2;
    }
  });

  keywords.topics.forEach((topic) => {
    if (content.includes(topic)) {
      score += 3;
    }
  });

  return score;
}

/**
 * Smart loading: Get relevant conversations based on user query
 */
function getRelevantConversations(userMessage, maxConversations = 200) {
  const allConversations = loadAllConversations();

  if (allConversations.length === 0) {
    return [];
  }

  // Extract keywords from user message
  const keywords = extractKeywords(userMessage);

  // If no specific keywords found, return recent conversations
  const hasSpecificKeywords =
    keywords.people.length > 0 ||
    keywords.companies.length > 0 ||
    keywords.topics.length > 0;

  if (!hasSpecificKeywords) {
    console.log('📝 No specific keywords found, loading recent conversations');
    return allConversations.slice(-maxConversations);
  }

  // Score all conversations
  const scoredConversations = allConversations.map((conv) => ({
    ...conv,
    relevanceScore: calculateRelevanceScore(conv, keywords),
  }));

  // Sort by relevance score (descending) and then by date (recent first)
  scoredConversations.sort((a, b) => {
    if (a.relevanceScore !== b.relevanceScore) {
      return b.relevanceScore - a.relevanceScore;
    }
    // If same relevance, prefer more recent
    const dateA = parseTimestamp(a.timestamp);
    const dateB = parseTimestamp(b.timestamp);
    return dateB - dateA;
  });

  // Get top relevant conversations
  const relevantConversations = scoredConversations
    .filter((conv) => conv.relevanceScore > 0)
    .slice(0, Math.floor(maxConversations * 0.7)); // 70% relevant

  // Fill remaining slots with recent conversations if needed
  const recentConversations = allConversations
    .slice(-Math.floor(maxConversations * 0.3)) // 30% recent
    .filter(
      (conv) =>
        !relevantConversations.find((rel) => rel.timestamp === conv.timestamp)
    );

  const finalConversations = [
    ...relevantConversations,
    ...recentConversations,
  ].slice(0, maxConversations);

  console.log(
    `🎯 Smart loading found ${relevantConversations.length} relevant + ${recentConversations.length} recent conversations`
  );
  console.log(
    `🔍 Keywords: People: [${keywords.people.join(
      ', '
    )}], Companies: [${keywords.companies.join(
      ', '
    )}], Topics: [${keywords.topics.join(', ')}]`
  );

  return finalConversations;
}

// Load conversations with smart context selection
function loadCleanedConversations(userMessage = '') {
  try {
    // Use smart loading to get relevant conversations
    const selectedConversations = getRelevantConversations(userMessage, 200);

    if (selectedConversations.length === 0) {
      console.log('⚠️ No conversations loaded');
      return '';
    }

    // Format conversations for AI context
    let formattedConversations =
      '\n\n## SMART-LOADED MARKET CONVERSATION HISTORY\n\n';
    formattedConversations += `🎯 You have access to ${selectedConversations.length} carefully selected conversations.\n`;
    formattedConversations +=
      '🔍 These conversations were chosen based on relevance to the current query and recent market activity.\n';
    formattedConversations +=
      '📅 Conversations are sorted by relevance and recency.\n\n';
    formattedConversations += '### 📊 SELECTED CONVERSATION TIMELINE:\n\n';

    selectedConversations.forEach((conv) => {
      const timestamp = conv.timestamp || 'Unknown time';
      const source = conv.sourceFile || 'Unknown source';
      const user = conv.user || conv.author || conv.sender || 'Unknown user';
      const content = conv.content || conv.message || 'No content';
      const relevanceScore = conv.relevanceScore || 0;

      formattedConversations += `**${timestamp}** [${source}] - ${user}`;
      if (relevanceScore > 0) {
        formattedConversations += ` (Relevance: ${relevanceScore})`;
      }
      formattedConversations += `: "${content}"\n\n`;
    });

    const totalChars = formattedConversations.length;
    const estimatedTokens = Math.ceil(totalChars / 4);
    console.log(
      `✅ Loaded smart conversation history: ${selectedConversations.length} conversations`
    );
    console.log(
      `📊 Total characters: ${totalChars}, Estimated tokens: ${estimatedTokens}`
    );

    return formattedConversations;
  } catch (error) {
    console.log('Could not load conversations:', error.message);
    return '';
  }
}

// Initialize AI service with fallback support
const aiService = new AIService();

// Conversation history functions
async function saveConversationExchange(
  userMessage,
  aiResponse,
  leadData,
  userId = null
) {
  try {
    // Save as both JSON (for system use) and TXT (for human readability)
    const conversationFileJson = path.join(
      __dirname,
      'data',
      'conversation_history.json'
    );
    const conversationFileTxt = path.join(
      __dirname,
      'data',
      'conversation_history.txt'
    );
    const dataDir = path.dirname(conversationFileJson);

    // Ensure data directory exists
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    let conversations = [];

    // Read existing conversations if JSON file exists
    if (fs.existsSync(conversationFileJson)) {
      try {
        const data = fs.readFileSync(conversationFileJson, 'utf8');
        const encryptedConversations = JSON.parse(data);

        // Decrypt existing conversations
        conversations = encryptedConversations
          .map((conv) => {
            try {
              return decryptConversation(conv);
            } catch (error) {
              console.warn('Failed to decrypt conversation:', error.message);
              return null;
            }
          })
          .filter((conv) => conv !== null);
      } catch (error) {
        console.error('Error reading conversation history:', error);
      }
    }

    // Add new conversation exchange
    const exchange = {
      id: encryptionService.generateSecureToken(16),
      timestamp: new Date().toISOString(),
      userMessage,
      aiResponse,
      leadData: leadData || {},
      userId: userId,
      sessionId: Date.now().toString(),
    };

    conversations.push(exchange);

    // Keep only last 100 exchanges to prevent file from getting too large
    if (conversations.length > 100) {
      conversations = conversations.slice(-100);
    }

    // Encrypt conversations before saving JSON (for system use)
    const encryptedConversations = conversations.map((conv) =>
      encryptConversation(conv)
    );
    fs.writeFileSync(
      conversationFileJson,
      JSON.stringify(encryptedConversations, null, 2)
    );

    // Save as readable text file
    await saveConversationAsText(conversations, conversationFileTxt);

    console.log('💾 Conversation saved securely (JSON + TXT)');
  } catch (error) {
    console.error(
      'Error saving conversation exchange:',
      anonymizeForLogging(error)
    );
  }
}

// New function to save conversations as readable text
async function saveConversationAsText(conversations, filePath) {
  try {
    let textContent = '';
    textContent += '='.repeat(80) + '\n';
    textContent += 'ECHO VOICE LEADS - CONVERSATION HISTORY\n';
    textContent += '='.repeat(80) + '\n';
    textContent += `Generated: ${new Date().toLocaleString()}\n`;
    textContent += `Total Conversations: ${conversations.length}\n`;
    textContent += '='.repeat(80) + '\n\n';

    conversations.forEach((conv, index) => {
      const timestamp = new Date(conv.timestamp).toLocaleString();
      const sessionId = conv.sessionId || 'Unknown';
      const userId = conv.userId || 'Anonymous';

      textContent += `CONVERSATION #${index + 1}\n`;
      textContent += '-'.repeat(40) + '\n';
      textContent += `Timestamp: ${timestamp}\n`;
      textContent += `Session ID: ${sessionId}\n`;
      textContent += `User ID: ${userId}\n`;

      // Add lead data if present
      if (conv.leadData && Object.keys(conv.leadData).length > 0) {
        textContent += `Lead Data: ${JSON.stringify(conv.leadData, null, 2)}\n`;
      }

      textContent += '\n';
      textContent += `USER: ${conv.userMessage}\n\n`;
      textContent += `AI RESPONSE: ${conv.aiResponse}\n\n`;
      textContent += '='.repeat(80) + '\n\n';
    });

    // Write to text file
    fs.writeFileSync(filePath, textContent, 'utf8');

    console.log(`📝 Text conversation history saved to: ${filePath}`);
  } catch (error) {
    console.error('Error saving conversation as text:', error);
  }
}

async function getConversationSummary() {
  try {
    const conversationFile = path.join(
      __dirname,
      'data',
      'conversation_history.json'
    );

    if (!fs.existsSync(conversationFile)) {
      return "I don't have any previous conversation history to summarize yet. This appears to be our first interaction!";
    }

    const data = fs.readFileSync(conversationFile, 'utf8');
    const encryptedConversations = JSON.parse(data);

    // Decrypt conversations
    const conversations = encryptedConversations
      .map((conv) => {
        try {
          return decryptConversation(conv);
        } catch (error) {
          console.warn('Failed to decrypt conversation:', error.message);
          return null;
        }
      })
      .filter((conv) => conv !== null);

    if (conversations.length === 0) {
      return "I don't have any previous conversation history to summarize yet.";
    }

    // Get conversations from the last week
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const recentConversations = conversations.filter(
      (conv) => new Date(conv.timestamp) > oneWeekAgo
    );

    if (recentConversations.length === 0) {
      return "I don't have any conversations from the last week to summarize.";
    }

    // Create summary
    const leadInfo = recentConversations
      .map((conv) => conv.leadData)
      .filter((data) => data && Object.keys(data).length > 0)
      .pop(); // Get the most recent lead data

    const keyTopics = recentConversations
      .map((conv) => conv.userMessage)
      .join(' ')
      .toLowerCase();

    let summary = `Here's a summary of our recent conversations:\n\n`;
    summary += `📅 **Time Period**: Last week (${recentConversations.length} exchanges)\n\n`;

    if (leadInfo && Object.keys(leadInfo).length > 0) {
      summary += `👤 **Lead Information Collected**:\n`;
      if (leadInfo.name) summary += `- Name: ${leadInfo.name}\n`;
      if (leadInfo.email) summary += `- Email: ${leadInfo.email}\n`;
      if (leadInfo.phone) summary += `- Phone: ${leadInfo.phone}\n`;
      summary += `\n`;
    }

    summary += `💬 **Key Discussion Topics**:\n`;
    if (keyTopics.includes('invest') || keyTopics.includes('stock')) {
      summary += `- Investment interests and stock market discussions\n`;
    }
    if (keyTopics.includes('portfolio') || keyTopics.includes('manage')) {
      summary += `- Portfolio management and investment strategies\n`;
    }
    if (keyTopics.includes('beginner') || keyTopics.includes('new')) {
      summary += `- Beginner investment guidance and education\n`;
    }
    if (keyTopics.includes('advisor') || keyTopics.includes('consultation')) {
      summary += `- Financial advisor services and consultation scheduling\n`;
    }

    summary += `\n📊 **Next Steps**: Based on our conversations, I'd recommend scheduling a consultation to discuss your investment goals in more detail.`;

    return summary;
  } catch (error) {
    console.error('Error getting conversation summary:', error);
    return "I'm having trouble accessing the conversation history right now, but I'm here to help with any investment questions you have!";
  }
}

async function getPreviousConversationContext(leadData) {
  try {
    const conversationFile = path.join(
      __dirname,
      'data',
      'conversation_history.json'
    );

    if (!fs.existsSync(conversationFile)) {
      return null;
    }

    const data = fs.readFileSync(conversationFile, 'utf8');
    const encryptedConversations = JSON.parse(data);

    // Decrypt conversations
    const conversations = encryptedConversations
      .map((conv) => {
        try {
          return decryptConversation(conv);
        } catch (error) {
          console.warn('Failed to decrypt conversation:', error.message);
          return null;
        }
      })
      .filter((conv) => conv !== null);

    if (conversations.length === 0) {
      return null;
    }

    // Try to find conversations for this specific lead
    let relevantConversations = [];

    if (leadData && (leadData.email || leadData.phone || leadData.name)) {
      // Find conversations with matching lead data
      relevantConversations = conversations.filter((conv) => {
        const convLead = conv.leadData || {};
        return (
          (leadData.email && convLead.email === leadData.email) ||
          (leadData.phone && convLead.phone === leadData.phone) ||
          (leadData.name && convLead.name === leadData.name)
        );
      });
    }

    // If no specific lead match, get recent conversations (last 24 hours)
    if (relevantConversations.length === 0) {
      const oneDayAgo = new Date();
      oneDayAgo.setDate(oneDayAgo.getDate() - 1);

      relevantConversations = conversations
        .filter((conv) => new Date(conv.timestamp) > oneDayAgo)
        .slice(-5); // Last 5 exchanges
    }

    if (relevantConversations.length === 0) {
      return null;
    }

    // Create context summary
    let context = 'Previous conversation context:\n';

    const lastLead = relevantConversations
      .map((conv) => conv.leadData)
      .filter((data) => data && Object.keys(data).length > 0)
      .pop();

    if (lastLead && Object.keys(lastLead).length > 0) {
      context += `Lead info: ${JSON.stringify(lastLead)}\n`;
    }

    // Add key conversation points
    const recentExchanges = relevantConversations.slice(-3);
    context += 'Recent discussion points:\n';
    recentExchanges.forEach((conv) => {
      context += `User: ${conv.userMessage.substring(0, 100)}...\n`;
      context += `You: ${conv.aiResponse.substring(0, 100)}...\n`;
    });

    return context;
  } catch (error) {
    console.error('Error getting previous conversation context:', error);
    return null;
  }
}

const app = express();
const PORT = process.env.PORT || 3000;

// Security Middleware
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", 'https://cdnjs.cloudflare.com'],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", 'data:', 'https:'],
        connectSrc: [
          "'self'",
          'https://api.openai.com',
          'https://api.anthropic.com',
        ],
        fontSrc: ["'self'", 'https://cdnjs.cloudflare.com'],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'", 'blob:', 'data:'],
        frameSrc: ["'none'"],
      },
    },
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true,
    },
  })
);

// CORS configuration
const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [
  'http://localhost:3000',
  'http://127.0.0.1:3000',
];

app.use(
  cors({
    origin: function (origin, callback) {
      // Allow requests with no origin (mobile apps, etc.)
      if (!origin) return callback(null, true);

      if (
        allowedOrigins.indexOf(origin) !== -1 ||
        process.env.NODE_ENV === 'development'
      ) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    optionsSuccessStatus: 200,
  })
);

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  message: {
    error: 'Too many requests from this IP',
    retryAfter: '15 minutes',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/', limiter);

// Body parsing with limits
app.use(
  express.json({
    limit: '10mb',
    verify: (req, res, buf) => {
      // Store raw body for signature verification if needed
      req.rawBody = buf;
    },
  })
);

app.use(
  express.urlencoded({
    extended: true,
    limit: '10mb',
  })
);

// Cookie parser for access control
app.use(cookieParser());

// Apply access control to all routes except access-related ones
app.use(requireAccessPassword);

// Static files
app.use(
  express.static('public', {
    maxAge: process.env.NODE_ENV === 'production' ? '1d' : 0,
    etag: true,
  })
);

// Force HTTPS in production
if (process.env.NODE_ENV === 'production') {
  app.use((req, res, next) => {
    if (req.header('x-forwarded-proto') !== 'https') {
      res.redirect(`https://${req.header('host')}${req.url}`);
    } else {
      next();
    }
  });
}

// Secure file upload configuration is imported from middleware/fileUpload.js

// Authentication Routes
app.post(
  '/api/auth/register',
  validateUserRegistration(),
  handleValidationErrors,
  async (req, res) => {
    try {
      const { username, email, password } = req.body;

      const user = await authService.createUser({
        username,
        email,
        password,
        role: 'user',
      });

      res.status(201).json({
        success: true,
        message: 'User created successfully',
        user,
      });
    } catch (error) {
      console.error('Registration error:', anonymizeForLogging(error));
      res.status(400).json({
        error: error.message || 'Registration failed',
        timestamp: new Date().toISOString(),
      });
    }
  }
);

app.post(
  '/api/auth/login',
  validateUserLogin(),
  handleValidationErrors,
  async (req, res) => {
    try {
      const { username, password } = req.body;

      const authResult = await authService.authenticateUser(username, password);

      if (!authResult) {
        return res.status(401).json({
          error: 'Invalid credentials',
          timestamp: new Date().toISOString(),
        });
      }

      res.json({
        success: true,
        message: 'Login successful',
        ...authResult,
      });
    } catch (error) {
      console.error('Login error:', anonymizeForLogging(error));
      res.status(500).json({
        error: 'Login failed',
        timestamp: new Date().toISOString(),
      });
    }
  }
);

// Access Control Routes (before access protection)
app.post(
  '/api/access/verify',
  validateAccessPasswordInput(),
  handleValidationErrors,
  async (req, res) => {
    try {
      const { password } = req.body;

      const isValid = await accessControlService.validateAccessPassword(
        password
      );

      if (!isValid) {
        return res.status(401).json({
          error: 'Invalid access password',
          timestamp: new Date().toISOString(),
        });
      }

      // Create access session
      const accessToken = accessControlService.createAccessSession(req);

      // Set secure cookie
      res.cookie('accessToken', accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge:
          parseInt(process.env.ACCESS_SESSION_TIMEOUT) || 24 * 60 * 60 * 1000,
      });

      res.json({
        success: true,
        message: 'Access granted',
        expiresIn:
          parseInt(process.env.ACCESS_SESSION_TIMEOUT) || 24 * 60 * 60 * 1000,
      });
    } catch (error) {
      console.error('Access verification error:', anonymizeForLogging(error));
      res.status(500).json({
        error: 'Access verification failed',
        timestamp: new Date().toISOString(),
      });
    }
  }
);

app.get('/api/access/status', (req, res) => {
  const accessToken = req.cookies?.accessToken || req.headers['x-access-token'];
  const isValid = accessControlService.isValidAccessSession(accessToken);

  res.json({
    hasAccess: isValid,
    stats: accessControlService.getAccessStats(),
    timestamp: new Date().toISOString(),
  });
});

app.post('/api/access/logout', (req, res) => {
  const accessToken = req.cookies?.accessToken || req.headers['x-access-token'];

  if (accessToken) {
    accessControlService.revokeAccessSession(accessToken);
  }

  res.clearCookie('accessToken');
  res.json({
    success: true,
    message: 'Access revoked',
  });
});

// Serve static files
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Serve access page
app.get('/access', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'access.html'));
});

// API Routes
app.post(
  '/api/speech-to-text',
  upload.single('audio'),
  additionalSecurityChecks,
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No audio file provided' });
      }

      console.log('Received audio file:', {
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
        filename: req.file.filename,
      });

      // Determine the correct file extension for OpenAI
      let fileExtension = '.wav'; // Default to WAV
      if (req.file.mimetype.includes('mp4')) {
        fileExtension = '.mp4';
      } else if (req.file.mimetype.includes('mpeg')) {
        fileExtension = '.mp3';
      } else if (req.file.mimetype.includes('ogg')) {
        fileExtension = '.ogg';
      } else if (req.file.mimetype.includes('webm')) {
        fileExtension = '.webm';
      }

      // Create a new filename with proper extension
      const newFilePath = req.file.path + fileExtension;
      fs.renameSync(req.file.path, newFilePath);

      // Create a readable stream from the renamed file
      const audioStream = fs.createReadStream(newFilePath);

      // Use AI service for speech-to-text
      const transcriptionResult = await aiService.speechToText(audioStream, {
        language: 'en',
      });

      // Clean up the renamed file
      fs.unlinkSync(newFilePath);

      res.json({
        transcription: transcriptionResult.text,
        success: true,
        provider: transcriptionResult.provider,
      });
    } catch (error) {
      console.error('Speech-to-text error:', error);

      // Clean up file if it exists (check both original and renamed paths)
      if (req.file) {
        if (fs.existsSync(req.file.path)) {
          fs.unlinkSync(req.file.path);
        }
        const newFilePath =
          req.file.path +
          (req.file.mimetype.includes('webm') ? '.webm' : '.wav');
        if (fs.existsSync(newFilePath)) {
          fs.unlinkSync(newFilePath);
        }
      }

      // Provide user-friendly error messages
      let userMessage =
        "Sorry, I couldn't process your audio. Please try speaking again or type your message.";
      let statusCode = 500;

      if (error.message?.includes('temporarily unavailable')) {
        userMessage = error.message; // Use the specific message from AI service
        statusCode = 503;
      } else if (error.message?.includes('not configured')) {
        userMessage =
          'Voice recognition is temporarily unavailable. Please type your message instead.';
        statusCode = 503;
      }

      res.status(statusCode).json({
        error: userMessage,
        details: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }
);

app.post(
  '/api/chat',
  validateMessage(),
  validateLeadData(),
  validateConversationHistory(),
  handleValidationErrors,
  async (req, res) => {
    try {
      const { message, context, leadData, conversationHistory } = req.body;

      if (!message) {
        return res.status(400).json({ error: 'No message provided' });
      }

      // Check if user is asking about conversation history
      const historyKeywords = [
        'what was discussed',
        'last week',
        'previous conversation',
        'chat history',
        'what did we talk about',
      ];
      const isHistoryRequest = historyKeywords.some((keyword) =>
        message.toLowerCase().includes(keyword.toLowerCase())
      );

      if (isHistoryRequest) {
        // Load and summarize conversation history
        const conversationSummary = await getConversationSummary();
        return res.json({
          response: conversationSummary,
          success: true,
        });
      }

      // Build conversation context
      let systemPrompt = STOCK_BUSINESS_SYSTEM_PROMPT;

      // Add smart-loaded conversation data for reference (context-aware loading)
      const cleanedConversations = loadCleanedConversations(message);
      if (cleanedConversations) {
        systemPrompt += cleanedConversations;
        console.log(
          '✅ Added smart-loaded conversation history to system prompt'
        );
        console.log(
          '📝 First 500 chars of conversation data:',
          cleanedConversations.substring(0, 500)
        );
      } else {
        console.log('❌ No conversation data loaded');
      }

      // Load previous conversation context for this lead
      const previousContext = await getPreviousConversationContext(leadData);
      if (previousContext) {
        systemPrompt += `\n\nPrevious conversation context: ${previousContext}`;
      }

      if (leadData && Object.keys(leadData).length > 0) {
        systemPrompt += `\n\nCurrent lead information: ${JSON.stringify(
          leadData
        )}`;
      }

      // Add a random market insight occasionally to provide value
      if (Math.random() < 0.3) {
        const insight =
          MARKET_INSIGHTS[Math.floor(Math.random() * MARKET_INSIGHTS.length)];
        systemPrompt += `\n\nConsider sharing this insight if relevant: "${insight}"`;
      }

      // Debug: Log final system prompt details
      console.log('🔍 Final system prompt length:', systemPrompt.length);
      console.log('🔍 Contains GR messages:', systemPrompt.includes('GR'));
      console.log('🔍 Contains Valuation:', systemPrompt.includes('Valuation'));
      console.log(
        '🔍 Last 200 chars of system prompt:',
        systemPrompt.slice(-200)
      );

      // Build messages array with conversation history
      const messages = [{ role: 'system', content: systemPrompt }];

      // Add conversation history if provided
      if (conversationHistory && Array.isArray(conversationHistory)) {
        messages.push(...conversationHistory);
      }

      // Add current user message
      messages.push({ role: 'user', content: message });

      // Use AI service with automatic fallback (increased tokens for detailed responses)
      const aiResult = await aiService.chatCompletion({
        model: 'gpt-4o',
        messages: messages,
        max_tokens: 1000,
        temperature: 0.7,
        presence_penalty: 0.1,
        frequency_penalty: 0.1,
      });

      const response = aiResult.response;

      // Save conversation to history
      await saveConversationExchange(message, response, leadData);

      // Include provider information in response for debugging
      const responseData = {
        response,
        success: true,
        provider: aiResult.provider,
      };

      // Add fallback information if applicable
      if (aiResult.fallback) {
        responseData.fallback = true;
        responseData.fallbackReason = aiResult.fallbackReason;
        console.log(
          `🔄 Used fallback provider: ${aiResult.provider}, reason: ${aiResult.fallbackReason}`
        );
      }

      res.json(responseData);
    } catch (error) {
      console.error('Chat error:', error);

      // Provide user-friendly error messages
      let userMessage =
        "I apologize, but I'm experiencing technical difficulties right now. Please try again in a moment.";
      let statusCode = 500;

      if (error.message?.includes('Both providers failed')) {
        userMessage =
          'Our AI services are temporarily unavailable. Please try again later.';
        statusCode = 503;
      } else if (error.message?.includes('No AI providers available')) {
        userMessage =
          'AI services are not configured properly. Please contact support.';
        statusCode = 503;
      } else if (error.message?.includes('rate limit')) {
        userMessage =
          "We're experiencing high demand. Please wait a moment and try again.";
        statusCode = 429;
      }

      res.status(statusCode).json({
        error: userMessage,
        details: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }
);

app.post(
  '/api/text-to-speech',
  validateTextToSpeech(),
  handleValidationErrors,
  async (req, res) => {
    try {
      console.log('🔊 TTS request received:', {
        textLength: req.body?.text?.length || 0,
      });
      const { text } = req.body;

      if (!text) {
        return res.status(400).json({ error: 'No text provided' });
      }

      // Use AI service for text-to-speech
      const speechResult = await aiService.textToSpeech(text, {
        model: 'tts-1',
        voice: 'alloy', // Professional, clear voice
        speed: 1.0,
      });

      // Set appropriate headers for audio response
      res.set({
        'Content-Type': 'audio/mpeg',
        'Content-Length': speechResult.buffer.length,
        'Cache-Control': 'no-cache',
      });

      res.send(speechResult.buffer);
    } catch (error) {
      console.error('Text-to-speech error:', error);

      // Provide user-friendly error messages
      let userMessage =
        "Sorry, I couldn't generate audio for this response. The text is still available above.";
      let statusCode = 500;

      if (error.message?.includes('temporarily unavailable')) {
        userMessage = error.message; // Use the specific message from AI service
        statusCode = 503;
      } else if (error.message?.includes('not configured')) {
        userMessage =
          'Voice synthesis is temporarily unavailable. The text response is still available.';
        statusCode = 503;
      }

      res.status(statusCode).json({
        error: userMessage,
        details: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }
);

// Conversation History endpoints
app.get('/api/conversation-history', (req, res) => {
  try {
    const conversationFile = path.join(DATA_DIR, 'conversation_history.json');

    if (!fs.existsSync(conversationFile)) {
      return res.json({
        success: true,
        conversations: [],
      });
    }

    const data = fs.readFileSync(conversationFile, 'utf8');
    const encryptedConversations = JSON.parse(data);

    // Decrypt conversations for the user
    const conversations = encryptedConversations
      .map((conv) => {
        try {
          const decrypted = decryptConversation(conv);
          // Filter by user if not admin
          if (
            req.user.role !== 'admin' &&
            decrypted.userId !== req.user.userId
          ) {
            return null;
          }
          return decrypted;
        } catch (error) {
          console.warn('Failed to decrypt conversation:', error.message);
          return null;
        }
      })
      .filter((conv) => conv !== null);

    res.json({
      success: true,
      conversations: conversations || [],
    });
  } catch (error) {
    console.error(
      'Error reading conversation history:',
      anonymizeForLogging(error)
    );
    res.status(500).json({
      success: false,
      error: 'Failed to load conversation history',
    });
  }
});

app.delete('/api/conversation-history', (req, res) => {
  try {
    const conversationFileJson = path.join(
      DATA_DIR,
      'conversation_history.json'
    );
    const conversationFileTxt = path.join(
      __dirname,
      'data',
      'conversation_history.txt'
    );

    // Clear both JSON and TXT files
    if (fs.existsSync(conversationFileJson)) {
      fs.writeFileSync(conversationFileJson, JSON.stringify([], null, 2));
    }

    if (fs.existsSync(conversationFileTxt)) {
      fs.unlinkSync(conversationFileTxt);
    }

    res.json({
      success: true,
      message:
        'Conversation history cleared successfully (both JSON and TXT files)',
    });
  } catch (error) {
    console.error('Error clearing conversation history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear conversation history',
    });
  }
});

// New endpoint to download conversation history as text file
app.get('/api/conversation-history/download', (req, res) => {
  try {
    const conversationFileTxt = path.join(DATA_DIR, 'conversation_history.txt');

    if (!fs.existsSync(conversationFileTxt)) {
      return res.status(404).json({
        success: false,
        error: 'No conversation history text file found',
      });
    }

    // Set headers for file download
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="conversation_history_${
        new Date().toISOString().split('T')[0]
      }.txt"`
    );

    // Send the file
    res.sendFile(conversationFileTxt);
  } catch (error) {
    console.error('Error downloading conversation history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to download conversation history',
    });
  }
});

// AI Service Status endpoint
app.get('/api/ai-status', (req, res) => {
  try {
    const status = aiService.getStatus();
    res.json({
      success: true,
      ...status,
    });
  } catch (error) {
    console.error('AI status error:', error);
    res.status(500).json({
      error: 'Failed to get AI service status',
      details: error.message,
    });
  }
});

// AI Health Check endpoint
app.get('/api/ai-health', async (req, res) => {
  try {
    const provider = req.query.provider || 'openai';
    const healthResult = await aiService.healthCheck(provider);

    res.json({
      success: true,
      ...healthResult,
    });
  } catch (error) {
    console.error('AI health check error:', error);
    res.status(500).json({
      error: 'Failed to perform health check',
      details: error.message,
    });
  }
});

// Stock detection endpoint
app.post('/api/detect-stocks', async (req, res) => {
  try {
    const { text } = req.body;

    if (!text || typeof text !== 'string') {
      return res.status(400).json({
        error: 'Invalid input',
        message: 'Text parameter is required and must be a string'
      });
    }

    // Use the sophisticated stock detection service
    const detectedStocks = detectStocksInText(text);

    // Add additional stock data if needed
    const enrichedStocks = detectedStocks.map(stock => ({
      ...stock,
      // Add any additional data processing here
      detectedAt: new Date().toISOString()
    }));

    res.json({
      success: true,
      stocks: enrichedStocks,
      count: enrichedStocks.length,
      text: text.substring(0, 100) + (text.length > 100 ? '...' : '') // For debugging
    });

  } catch (error) {
    console.error('Stock detection error:', error);
    res.status(500).json({
      error: 'Failed to detect stocks',
      details: error.message
    });
  }
});

// Test endpoint for stock detection
app.get('/api/test-stock-detection', async (req, res) => {
  try {
    const testText = req.query.text || "I'm interested in Apple (AAPL), Tesla (TSLA), and NVIDIA (NVDA) for my portfolio.";

    const detectedStocks = detectStocksInText(testText);

    res.json({
      success: true,
      testText,
      detectedStocks,
      count: detectedStocks.length,
      message: `Detected ${detectedStocks.length} stocks in test text`
    });
  } catch (error) {
    console.error('Test stock detection error:', error);
    res.status(500).json({
      error: 'Failed to test stock detection',
      details: error.message
    });
  }
});

app.post('/api/save-lead', async (req, res) => {
  try {
    const leadData = req.body;

    // Add timestamp
    leadData.timestamp = new Date().toISOString();
    leadData.id = Date.now().toString();

    // In a real application, you would save to a database
    // For now, we'll save to a JSON file
    const leadsFile = path.join(__dirname, 'leads.json');
    let leads = [];

    // Read existing leads if file exists
    if (fs.existsSync(leadsFile)) {
      try {
        const data = fs.readFileSync(leadsFile, 'utf8');
        leads = JSON.parse(data);
      } catch (error) {
        console.error('Error reading leads file:', error);
      }
    }

    // Add new lead
    leads.push(leadData);

    // Save back to file
    fs.writeFileSync(leadsFile, JSON.stringify(leads, null, 2));

    res.json({
      success: true,
      message: 'Lead saved successfully',
      leadId: leadData.id,
    });
  } catch (error) {
    console.error('Save lead error:', error);
    res.status(500).json({
      error: 'Failed to save lead',
      details: error.message,
    });
  }
});

// Global error handling middleware
app.use((error, req, res, next) => {
  // Log error securely (without sensitive data)
  const errorLog = {
    message: error.message,
    stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    timestamp: new Date().toISOString(),
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    url: req.url,
    method: req.method,
    userId: req.user?.userId || 'anonymous',
  };

  console.error('Application error:', anonymizeForLogging(errorLog));

  // Send generic error response
  const statusCode = error.status || error.statusCode || 500;
  const message = statusCode === 500 ? 'Internal server error' : error.message;

  res.status(statusCode).json({
    error: message,
    timestamp: new Date().toISOString(),
    requestId: encryptionService.generateSecureToken(8),
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    timestamp: new Date().toISOString(),
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start server
const server = app.listen(PORT, '0.0.0.0', () => {
  const os = require('os');
  const networkInterfaces = os.networkInterfaces();
  let networkIP = 'localhost';

  // Find the first non-internal IPv4 address
  for (const interfaceName in networkInterfaces) {
    const addresses = networkInterfaces[interfaceName];
    for (const address of addresses) {
      if (address.family === 'IPv4' && !address.internal) {
        networkIP = address.address;
        break;
      }
    }
    if (networkIP !== 'localhost') break;
  }

  console.log('🚀 Echo Voice Leads server starting...');
  console.log(`📍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(
    `🔐 Security: ${
      process.env.NODE_ENV === 'production' ? 'PRODUCTION' : 'DEVELOPMENT'
    } mode`
  );
  console.log(`🌐 Server running on port ${PORT}`);
  console.log(`📱 Local:   http://localhost:${PORT}`);

  if (process.env.NODE_ENV !== 'production') {
    console.log(`🌍 Network: http://${networkIP}:${PORT}`);
    console.log(
      '⚠️  Access from other devices on the same WiFi using the Network URL'
    );
    console.log(
      '⚠️  Default admin credentials: admin/admin123 (CHANGE IMMEDIATELY!)'
    );
  }

  console.log('✅ Server ready for connections');
});

// Handle server errors
server.on('error', (error) => {
  console.error('Server error:', error);
  process.exit(1);
});
