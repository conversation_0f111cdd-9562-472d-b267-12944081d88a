/**
 * Integration Example: How to use Stock Detection Service in server.js
 * 
 * This file shows how to integrate the stock detection system into the existing
 * ShareFlix voice-driven investment platform.
 */

const { detectStocksInText, getStockInfo, searchStocks } = require('./services/stockDetectionService');

/**
 * Example 1: Integrate stock detection into the chat API response
 * This would be added to the /api/chat endpoint in server.js
 */
function enhanceChatResponseWithStockDetection(aiResponse, userMessage) {
  console.log('🔍 Analyzing AI response for stock mentions...');
  
  // Detect stocks mentioned in the AI response
  const detectedStocks = detectStocksInText(aiResponse);
  
  // Also check user message for context
  const userStocks = detectStocksInText(userMessage);
  
  // Combine and deduplicate
  const allStocks = [...detectedStocks, ...userStocks];
  const uniqueStocks = allStocks.filter((stock, index, self) => 
    index === self.findIndex(s => s.ticker === stock.ticker)
  );
  
  console.log(`📊 Detected ${uniqueStocks.length} unique stocks in conversation`);
  
  return {
    response: aiResponse,
    detectedStocks: uniqueStocks,
    stockContext: uniqueStocks.length > 0 ? generateStockContext(uniqueStocks) : null
  };
}

/**
 * Example 2: Generate additional context for detected stocks
 */
function generateStockContext(detectedStocks) {
  const context = {
    summary: `Conversation mentioned ${detectedStocks.length} stock(s): ${detectedStocks.map(s => s.ticker).join(', ')}`,
    stocks: detectedStocks.map(stock => ({
      ticker: stock.ticker,
      name: stock.companyName,
      price: stock.price,
      change: stock.changePercent,
      target: stock.priceTarget,
      thesis: stock.thesis,
      confidence: stock.matchDetails?.confidence || 1.0
    })),
    totalMarketCap: calculateTotalMarketCap(detectedStocks),
    sectors: [...new Set(detectedStocks.map(s => s.sector))]
  };
  
  return context;
}

/**
 * Example 3: Smart follow-up suggestions based on detected stocks
 */
function generateFollowUpSuggestions(detectedStocks) {
  if (detectedStocks.length === 0) {
    return [
      "Would you like to explore some trending stocks?",
      "Are you interested in any particular sector?",
      "What's your investment timeline and risk tolerance?"
    ];
  }
  
  const suggestions = [];
  const sectors = [...new Set(detectedStocks.map(s => s.sector))];
  
  // Sector-based suggestions
  if (sectors.includes('Technology')) {
    suggestions.push("Would you like to explore other technology stocks?");
  }
  
  if (sectors.includes('Energy')) {
    suggestions.push("Are you interested in the energy transition theme?");
  }
  
  // Stock-specific suggestions
  detectedStocks.forEach(stock => {
    if (stock.ticker === 'TSLA') {
      suggestions.push("Would you like to know about Tesla's latest developments in autonomous driving?");
    }
    if (stock.ticker === 'NVDA') {
      suggestions.push("Are you interested in other AI-focused companies?");
    }
    if (stock.ticker === 'AAPL') {
      suggestions.push("Would you like to discuss Apple's services growth strategy?");
    }
  });
  
  return suggestions.slice(0, 3); // Limit to 3 suggestions
}

/**
 * Example 4: Enhanced conversation logging with stock context
 */
function logConversationWithStockContext(userMessage, aiResponse, sessionId) {
  const detectedStocks = detectStocksInText(`${userMessage} ${aiResponse}`);
  
  const logEntry = {
    timestamp: new Date().toISOString(),
    sessionId,
    userMessage,
    aiResponse,
    detectedStocks: detectedStocks.map(stock => ({
      ticker: stock.ticker,
      name: stock.companyName,
      matchedTerms: stock.matchDetails?.matchedTerms || [],
      confidence: stock.matchDetails?.confidence || 1.0
    })),
    stockCount: detectedStocks.length,
    sectors: [...new Set(detectedStocks.map(s => s.sector))]
  };
  
  console.log('📝 Conversation logged with stock context:', {
    stocks: logEntry.detectedStocks.length,
    sectors: logEntry.sectors.length
  });
  
  return logEntry;
}

/**
 * Example 5: Stock-aware lead qualification
 */
function enhanceLeadQualification(leadData, conversationHistory) {
  // Analyze all conversations for stock interests
  const allText = conversationHistory.map(conv => 
    `${conv.userMessage} ${conv.aiResponse}`
  ).join(' ');
  
  const stockInterests = detectStocksInText(allText);
  
  // Categorize interests
  const interests = {
    sectors: [...new Set(stockInterests.map(s => s.sector))],
    riskProfile: determineRiskProfile(stockInterests),
    investmentStyle: determineInvestmentStyle(stockInterests),
    preferredStocks: stockInterests.map(s => s.ticker),
    totalMarketCapInterest: calculateTotalMarketCap(stockInterests)
  };
  
  return {
    ...leadData,
    stockInterests: interests,
    qualificationScore: calculateQualificationScore(leadData, interests)
  };
}

/**
 * Helper function: Calculate total market cap
 */
function calculateTotalMarketCap(stocks) {
  return stocks.reduce((total, stock) => {
    const marketCap = parseFloat(stock.marketCap.replace(/[^0-9.]/g, ''));
    const multiplier = stock.marketCap.includes('T') ? 1000 : 
                     stock.marketCap.includes('B') ? 1 : 0.001;
    return total + (marketCap * multiplier);
  }, 0);
}

/**
 * Helper function: Determine risk profile from stock interests
 */
function determineRiskProfile(stocks) {
  const riskScores = stocks.map(stock => {
    if (stock.sector === 'Technology' && stock.peRatio > 50) return 'high';
    if (stock.sector === 'Biotechnology') return 'high';
    if (stock.dividendYield > 3) return 'low';
    if (stock.peRatio < 20) return 'low';
    return 'medium';
  });
  
  const highRisk = riskScores.filter(r => r === 'high').length;
  const lowRisk = riskScores.filter(r => r === 'low').length;
  
  if (highRisk > lowRisk) return 'aggressive';
  if (lowRisk > highRisk) return 'conservative';
  return 'moderate';
}

/**
 * Helper function: Determine investment style
 */
function determineInvestmentStyle(stocks) {
  const growthStocks = stocks.filter(s => s.peRatio > 30).length;
  const valueStocks = stocks.filter(s => s.peRatio < 20).length;
  const dividendStocks = stocks.filter(s => s.dividendYield > 2).length;
  
  if (growthStocks > valueStocks && growthStocks > dividendStocks) return 'growth';
  if (valueStocks > growthStocks && valueStocks > dividendStocks) return 'value';
  if (dividendStocks > growthStocks && dividendStocks > valueStocks) return 'income';
  return 'balanced';
}

/**
 * Helper function: Calculate qualification score
 */
function calculateQualificationScore(leadData, interests) {
  let score = 0;
  
  // Base score from lead data
  if (leadData.portfolioSize > 50000) score += 30;
  else if (leadData.portfolioSize > 10000) score += 20;
  else score += 10;
  
  // Stock interest bonus
  score += Math.min(interests.preferredStocks.length * 5, 25);
  
  // Sector diversity bonus
  score += Math.min(interests.sectors.length * 3, 15);
  
  // Market cap interest bonus
  if (interests.totalMarketCapInterest > 1000) score += 20; // $1T+
  else if (interests.totalMarketCapInterest > 100) score += 10; // $100B+
  
  return Math.min(score, 100);
}

/**
 * Example integration into existing server.js chat endpoint
 */
function integrateIntoServerJS() {
  return `
// Add this to your /api/chat endpoint in server.js:

const { detectStocksInText } = require('./services/stockDetectionService');

// After getting AI response, before sending to client:
const enhancedResponse = enhanceChatResponseWithStockDetection(aiResponse, message);

// Add stock context to conversation history
const conversationEntry = {
  timestamp: new Date().toISOString(),
  userMessage: message,
  aiResponse: aiResponse,
  detectedStocks: enhancedResponse.detectedStocks,
  sessionId: sessionId
};

// Generate follow-up suggestions
const followUpSuggestions = generateFollowUpSuggestions(enhancedResponse.detectedStocks);

// Return enhanced response
res.json({
  response: aiResponse,
  stockContext: enhancedResponse.stockContext,
  followUpSuggestions: followUpSuggestions,
  success: true
});
  `;
}

module.exports = {
  enhanceChatResponseWithStockDetection,
  generateStockContext,
  generateFollowUpSuggestions,
  logConversationWithStockContext,
  enhanceLeadQualification,
  integrateIntoServerJS
};
