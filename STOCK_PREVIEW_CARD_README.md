# Stock Preview Card Component

A minimal, elegant preview card that appears during AI speech when stocks are mentioned in the conversation.

## 🎯 Overview

The Stock Preview Card is a subtle, non-intrusive component that enhances the voice-driven investment experience by providing visual context when stocks are discussed. It appears automatically during AI speech and disappears gracefully when the speech ends.

## ✨ Features

### 🎨 **Design**
- **Minimal & Clean**: Company name + ticker symbol only
- **Subtle Appearance**: Semi-transparent dark background with blur effect
- **Non-intrusive**: Positioned to not interfere with conversation flow
- **Responsive**: Adapts to different screen sizes

### 🎭 **Animations**
- **Smooth Fade-in**: Slides up from bottom with cubic-bezier easing
- **Graceful Exit**: Slides down with fade-out animation
- **Auto-timing**: Appears during speech, disappears when audio ends
- **Performance Optimized**: Hardware-accelerated CSS animations

### 📱 **Responsive Design**
- **Mobile First**: Optimized for 400px container width
- **Small Screens**: Adjusted sizing for phones (≤375px)
- **Tablets**: Enhanced sizing for larger screens (≥768px)
- **Positioning**: Always centered horizontally above status indicator

## 🏗️ Structure

### HTML Structure
```html
<div class="stock-preview-card" id="stockPreviewCard" style="display: none;">
    <div class="stock-preview-content">
        <div class="stock-info">
            <span class="company-name" id="previewCompanyName">Apple Inc.</span>
            <span class="ticker-symbol" id="previewTicker">AAPL</span>
        </div>
    </div>
</div>
```

### CSS Classes
- `.stock-preview-card` - Main container with positioning and animations
- `.stock-preview-content` - Content wrapper with styling and backdrop blur
- `.stock-info` - Flexbox container for text elements
- `.company-name` - Company name styling
- `.ticker-symbol` - Ticker symbol with accent color

## 🎨 Styling Specifications

### **Colors**
- **Background**: `rgba(0, 0, 0, 0.85)` - Semi-transparent dark
- **Border**: `rgba(0, 212, 170, 0.2)` - Subtle teal accent
- **Company Name**: `#ffffff` - Pure white
- **Ticker Symbol**: `#00d4aa` - ShareFlix teal accent

### **Typography**
- **Company Name**: 0.85rem, font-weight 500, white
- **Ticker Symbol**: 0.75rem, font-weight 600, teal, letter-spacing 0.5px
- **Font Family**: System fonts (-apple-system, BlinkMacSystemFont, etc.)

### **Layout**
- **Positioning**: Fixed, bottom: 120px, centered horizontally
- **Padding**: 12px 16px (14px 18px on tablets)
- **Border Radius**: 12px
- **Min Width**: 140px (160px on tablets)
- **Box Shadow**: `0 8px 32px rgba(0, 0, 0, 0.4)`

## 🎬 Animation Details

### **Slide Up Fade In**
```css
@keyframes slideUpFade {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}
```

### **Slide Down Fade Out**
```css
@keyframes slideDownFade {
    0% {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
}
```

### **Timing**
- **Duration**: 0.4s for fade-in, 0.3s for fade-out
- **Easing**: `cubic-bezier(0.4, 0, 0.2, 1)` - Material Design standard
- **Auto-hide**: 4 seconds after appearance

## 🔧 JavaScript API

### **Core Methods**

#### `showStockPreview(companyName, ticker)`
Shows the stock preview card with specified content.
```javascript
app.showStockPreview('Apple Inc.', 'AAPL');
```

#### `hideStockPreview()`
Hides the stock preview card with animation.
```javascript
app.hideStockPreview();
```

#### `showStockPreviewForSpeech(detectedStocks)`
Shows preview for detected stocks during AI speech.
```javascript
app.showStockPreviewForSpeech([
    { companyName: 'Apple Inc.', ticker: 'AAPL', matchDetails: { confidence: 0.9 } }
]);
```

#### `handleStockPreviewForSpeech(aiResponseText)`
Analyzes AI response text and shows preview if stocks are detected.
```javascript
await app.handleStockPreviewForSpeech("I think Apple is a great investment...");
```

### **Integration Points**

#### **Text-to-Speech Integration**
```javascript
// In textToSpeech method
playPromise.then(async () => {
    console.log('✅ Audio auto-play successful!');
    await this.handleStockPreviewForSpeech(text);
});
```

#### **Audio End Handler**
```javascript
audioElement.addEventListener('ended', () => {
    this.hideStockPreview(); // Hide when speech ends
});
```

## 🧪 Testing

### **Test File**
Use `test-stock-preview.html` to test the component:
```bash
# Open in browser
open test-stock-preview.html
```

### **Test Cases**
1. **Show Animation**: Card slides up smoothly from bottom
2. **Hide Animation**: Card slides down with fade-out
3. **Auto-hide**: Card disappears after 4 seconds
4. **Multiple Stocks**: Shows highest confidence stock first
5. **Responsive**: Adapts to different screen sizes
6. **Positioning**: Stays centered and above status indicator

### **Manual Testing**
```javascript
// In browser console
app.showStockPreview('Tesla, Inc.', 'TSLA');
setTimeout(() => app.hideStockPreview(), 3000);
```

## 📱 Responsive Breakpoints

### **Small Mobile (≤375px)**
- Bottom: 110px
- Padding: 10px 14px
- Min Width: 120px
- Company Name: 0.8rem, max-width 100px
- Ticker: 0.7rem

### **Default Mobile (376px-767px)**
- Bottom: 120px
- Padding: 12px 16px
- Min Width: 140px
- Company Name: 0.85rem, max-width 120px
- Ticker: 0.75rem

### **Tablet+ (≥768px)**
- Bottom: 140px
- Padding: 14px 18px
- Min Width: 160px
- Company Name: 0.9rem, max-width 140px
- Ticker: 0.8rem

## 🔄 Integration Workflow

### **1. Stock Detection**
AI response text is analyzed for stock mentions using pattern matching or the stock detection service.

### **2. Preview Trigger**
When audio playback starts successfully, stock detection is triggered automatically.

### **3. Display Logic**
- Show highest confidence stock first
- Auto-hide after 4 seconds
- Hide immediately when audio ends

### **4. Animation Sequence**
1. Set display: block
2. Force reflow
3. Add .show and .animate-in classes
4. Remove .animate-out class
5. Auto-hide with reverse animation

## 🎯 Design Principles

### **Minimal Content**
- Only essential information: company name + ticker
- No prices, charts, or additional data
- Clean, readable typography

### **Non-intrusive Positioning**
- Fixed position above status indicator
- Doesn't block conversation content
- Subtle enough to not distract from voice interaction

### **Smooth Animations**
- Hardware-accelerated transforms
- Consistent timing and easing
- Graceful entrance and exit

### **Accessibility**
- High contrast text
- Readable font sizes
- Non-essential visual enhancement (doesn't break functionality if hidden)

## 🚀 Future Enhancements

- **Multiple Stock Carousel**: Show multiple detected stocks in sequence
- **Price Integration**: Optional price display for premium users
- **Click Interaction**: Tap to see more details
- **Voice Feedback**: Audio confirmation of stock detection
- **Customization**: User preferences for show/hide behavior
