const fs = require('fs');
const path = require('path');

class StockDetectionService {
  constructor() {
    this.stockData = null;
    this.keywordMap = new Map();
    this.tickerRegex = null;
    this.loadStockData();
  }

  /**
   * Load stock data and build keyword mappings
   */
  loadStockData() {
    try {
      const stockDataPath = path.join(__dirname, '..', 'stockData.json');
      const rawData = fs.readFileSync(stockDataPath, 'utf8');
      this.stockData = JSON.parse(rawData);
      
      this.buildKeywordMappings();
      this.buildTickerRegex();
      
      console.log(`📊 Stock detection service loaded ${this.stockData.stocks.length} stocks`);
      console.log(`🔍 Built ${this.keywordMap.size} keyword mappings`);
    } catch (error) {
      console.error('❌ Failed to load stock data:', error.message);
      this.stockData = { stocks: [] };
    }
  }

  /**
   * Build keyword mappings for efficient lookup
   */
  buildKeywordMappings() {
    this.keywordMap.clear();
    
    this.stockData.stocks.forEach(stock => {
      // Add ticker symbol (case insensitive)
      this.addKeywordMapping(stock.ticker.toLowerCase(), stock);
      
      // Add company name (case insensitive)
      this.addKeywordMapping(stock.companyName.toLowerCase(), stock);
      
      // Add all keywords
      stock.keywords.forEach(keyword => {
        this.addKeywordMapping(keyword.toLowerCase(), stock);
      });
      
      // Add common variations
      this.addCommonVariations(stock);
    });
  }

  /**
   * Add keyword mapping with collision handling
   */
  addKeywordMapping(keyword, stock) {
    if (this.keywordMap.has(keyword)) {
      // Handle collisions by storing array of stocks
      const existing = this.keywordMap.get(keyword);
      if (Array.isArray(existing)) {
        existing.push(stock);
      } else {
        this.keywordMap.set(keyword, [existing, stock]);
      }
    } else {
      this.keywordMap.set(keyword, stock);
    }
  }

  /**
   * Add common variations for stock names
   */
  addCommonVariations(stock) {
    const ticker = stock.ticker.toLowerCase();
    const companyName = stock.companyName.toLowerCase();
    
    // Add variations without common suffixes
    const suffixes = [' inc.', ' inc', ' corporation', ' corp.', ' corp', ' ltd.', ' ltd', ' plc', ' s.a.'];
    suffixes.forEach(suffix => {
      if (companyName.endsWith(suffix)) {
        const baseName = companyName.replace(suffix, '').trim();
        this.addKeywordMapping(baseName, stock);
      }
    });
    
    // Add ticker with dollar sign
    this.addKeywordMapping(`$${ticker}`, stock);
    
    // Add specific company variations
    this.addSpecificVariations(stock);
  }

  /**
   * Add specific variations for well-known companies
   */
  addSpecificVariations(stock) {
    const ticker = stock.ticker.toLowerCase();
    const variations = {
      'aapl': ['apple inc', 'apple computer'],
      'tsla': ['tesla motors', 'tesla inc'],
      'meta': ['facebook', 'fb'],
      'googl': ['google', 'alphabet'],
      'msft': ['microsoft', 'msft'],
      'amzn': ['amazon'],
      'nvda': ['nvidia corp'],
      'spot': ['spotify technology'],
      'pltr': ['palantir tech'],
      'coin': ['coinbase global']
    };
    
    if (variations[ticker]) {
      variations[ticker].forEach(variation => {
        this.addKeywordMapping(variation, stock);
      });
    }
  }

  /**
   * Build regex for ticker detection
   */
  buildTickerRegex() {
    const tickers = this.stockData.stocks.map(stock => stock.ticker);
    // Match tickers with optional $ prefix, word boundaries, and case insensitive
    this.tickerRegex = new RegExp(`\\b\\$?(${tickers.join('|')})\\b`, 'gi');
  }

  /**
   * Main detection function - scans text and returns matched stocks
   * @param {string} text - AI response text to scan
   * @returns {Array} Array of detected stock objects with match details
   */
  detectStocks(text) {
    if (!text || typeof text !== 'string') {
      return [];
    }

    const detectedStocks = new Map(); // Use Map to avoid duplicates
    const normalizedText = text.toLowerCase();
    
    // Method 1: Ticker symbol detection (highest confidence)
    this.detectTickers(text, detectedStocks);
    
    // Method 2: Company name detection
    this.detectCompanyNames(normalizedText, detectedStocks);
    
    // Method 3: Keyword detection
    this.detectKeywords(normalizedText, detectedStocks);
    
    // Convert Map to Array and add match metadata
    return Array.from(detectedStocks.values()).map(detection => ({
      ...detection.stock,
      matchDetails: {
        matchedTerms: detection.matchedTerms,
        matchTypes: detection.matchTypes,
        confidence: this.calculateConfidence(detection),
        positions: detection.positions
      }
    }));
  }

  /**
   * Detect ticker symbols in text
   */
  detectTickers(text, detectedStocks) {
    let match;
    while ((match = this.tickerRegex.exec(text)) !== null) {
      const ticker = match[1].toUpperCase();
      const stock = this.stockData.stocks.find(s => s.ticker === ticker);
      
      if (stock) {
        this.addDetection(detectedStocks, stock, {
          term: match[0],
          type: 'ticker',
          position: match.index,
          confidence: 0.95
        });
      }
    }
  }

  /**
   * Detect company names in text
   */
  detectCompanyNames(normalizedText, detectedStocks) {
    this.stockData.stocks.forEach(stock => {
      const companyName = stock.companyName.toLowerCase();
      const index = normalizedText.indexOf(companyName);
      
      if (index !== -1) {
        this.addDetection(detectedStocks, stock, {
          term: companyName,
          type: 'company_name',
          position: index,
          confidence: 0.85
        });
      }
    });
  }

  /**
   * Detect keywords in text with word boundary checking
   */
  detectKeywords(normalizedText, detectedStocks) {
    for (const [keyword, stockOrStocks] of this.keywordMap.entries()) {
      // Skip very short keywords that might cause false positives
      if (keyword.length < 3) continue;

      // Use word boundary regex for better matching
      const regex = new RegExp(`\\b${this.escapeRegex(keyword)}\\b`, 'gi');
      let match;

      while ((match = regex.exec(normalizedText)) !== null) {
        const stocks = Array.isArray(stockOrStocks) ? stockOrStocks : [stockOrStocks];

        stocks.forEach(stock => {
          this.addDetection(detectedStocks, stock, {
            term: keyword,
            type: 'keyword',
            position: match.index,
            confidence: 0.7
          });
        });
      }
    }
  }

  /**
   * Escape special regex characters
   */
  escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Add detection to results map
   */
  addDetection(detectedStocks, stock, matchInfo) {
    const key = stock.ticker;
    
    if (detectedStocks.has(key)) {
      const existing = detectedStocks.get(key);
      existing.matchedTerms.push(matchInfo.term);
      existing.matchTypes.push(matchInfo.type);
      existing.positions.push(matchInfo.position);
      existing.confidences.push(matchInfo.confidence);
    } else {
      detectedStocks.set(key, {
        stock,
        matchedTerms: [matchInfo.term],
        matchTypes: [matchInfo.type],
        positions: [matchInfo.position],
        confidences: [matchInfo.confidence]
      });
    }
  }

  /**
   * Calculate overall confidence score for a detection
   */
  calculateConfidence(detection) {
    const maxConfidence = Math.max(...detection.confidences);
    const typeBonus = detection.matchTypes.includes('ticker') ? 0.1 : 0;
    const multipleMatchBonus = detection.matchedTerms.length > 1 ? 0.05 : 0;
    
    return Math.min(1.0, maxConfidence + typeBonus + multipleMatchBonus);
  }

  /**
   * Get stock by ticker
   */
  getStockByTicker(ticker) {
    return this.stockData.stocks.find(stock => 
      stock.ticker.toLowerCase() === ticker.toLowerCase()
    );
  }

  /**
   * Search stocks by keyword
   */
  searchStocks(query) {
    const normalizedQuery = query.toLowerCase();
    const results = [];
    
    for (const [keyword, stockOrStocks] of this.keywordMap.entries()) {
      if (keyword.includes(normalizedQuery)) {
        const stocks = Array.isArray(stockOrStocks) ? stockOrStocks : [stockOrStocks];
        results.push(...stocks);
      }
    }
    
    // Remove duplicates
    return results.filter((stock, index, self) => 
      index === self.findIndex(s => s.ticker === stock.ticker)
    );
  }

  /**
   * Get all available stocks
   */
  getAllStocks() {
    return this.stockData.stocks;
  }

  /**
   * Reload stock data (useful for updates)
   */
  reload() {
    this.loadStockData();
  }

  /**
   * Get service statistics
   */
  getStats() {
    return {
      totalStocks: this.stockData.stocks.length,
      totalKeywords: this.keywordMap.size,
      lastUpdated: this.stockData.metadata?.lastUpdated,
      version: this.stockData.metadata?.version
    };
  }
}

// Create singleton instance
const stockDetectionService = new StockDetectionService();

/**
 * Convenience function for detecting stocks in text
 * @param {string} text - Text to analyze
 * @returns {Array} Array of detected stocks with match details
 */
function detectStocksInText(text) {
  return stockDetectionService.detectStocks(text);
}

/**
 * Get stock information by ticker
 * @param {string} ticker - Stock ticker symbol
 * @returns {Object|null} Stock object or null if not found
 */
function getStockInfo(ticker) {
  return stockDetectionService.getStockByTicker(ticker);
}

/**
 * Search for stocks by keyword
 * @param {string} query - Search query
 * @returns {Array} Array of matching stocks
 */
function searchStocks(query) {
  return stockDetectionService.searchStocks(query);
}

module.exports = {
  StockDetectionService,
  detectStocksInText,
  getStockInfo,
  searchStocks,
  stockDetectionService
};
