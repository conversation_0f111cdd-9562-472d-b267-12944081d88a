const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

class EncryptionService {
  constructor() {
    this.algorithm = 'aes-256-gcm';
    this.keyLength = 32; // 256 bits
    this.ivLength = 16; // 128 bits
    this.tagLength = 16; // 128 bits
    
    // Initialize encryption key
    this.encryptionKey = this.getOrCreateEncryptionKey();
  }

  getOrCreateEncryptionKey() {
    const keyFile = path.join(__dirname, '..', 'data', 'encryption.key');
    const keyDir = path.dirname(keyFile);
    
    // Ensure data directory exists
    if (!fs.existsSync(keyDir)) {
      fs.mkdirSync(keyDir, { recursive: true });
    }

    // Check if key exists in environment variable (preferred for production)
    if (process.env.ENCRYPTION_KEY) {
      const key = Buffer.from(process.env.ENCRYPTION_KEY, 'hex');
      if (key.length === this.keyLength) {
        return key;
      } else {
        console.warn('⚠️ ENCRYPTION_KEY in environment has invalid length. Generating new key.');
      }
    }

    // Check if key file exists
    if (fs.existsSync(keyFile)) {
      try {
        const keyData = fs.readFileSync(keyFile);
        if (keyData.length === this.keyLength) {
          console.log('🔐 Loaded encryption key from file');
          return keyData;
        }
      } catch (error) {
        console.warn('⚠️ Could not read encryption key file:', error.message);
      }
    }

    // Generate new key
    const newKey = crypto.randomBytes(this.keyLength);
    
    try {
      fs.writeFileSync(keyFile, newKey, { mode: 0o600 }); // Restrict file permissions
      console.log('🔐 Generated new encryption key and saved to file');
      console.warn('⚠️ For production, set ENCRYPTION_KEY environment variable with:', newKey.toString('hex'));
    } catch (error) {
      console.warn('⚠️ Could not save encryption key to file:', error.message);
    }

    return newKey;
  }

  encrypt(plaintext) {
    try {
      if (typeof plaintext !== 'string') {
        plaintext = JSON.stringify(plaintext);
      }

      const iv = crypto.randomBytes(this.ivLength);
      const cipher = crypto.createCipheriv('aes-256-cbc', this.encryptionKey, iv);

      let encrypted = cipher.update(plaintext, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      return {
        encrypted,
        iv: iv.toString('hex'),
        algorithm: this.algorithm
      };
    } catch (error) {
      console.error('Encryption error:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  decrypt(encryptedData) {
    try {
      if (!encryptedData || !encryptedData.encrypted || !encryptedData.iv) {
        throw new Error('Invalid encrypted data format');
      }

      const iv = Buffer.from(encryptedData.iv, 'hex');
      const decipher = crypto.createDecipheriv('aes-256-cbc', this.encryptionKey, iv);

      let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      console.error('Decryption error:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  // Encrypt sensitive fields in an object
  encryptSensitiveFields(data, sensitiveFields = []) {
    const result = { ...data };
    
    for (const field of sensitiveFields) {
      if (result[field] !== undefined) {
        result[field] = this.encrypt(result[field]);
      }
    }
    
    return result;
  }

  // Decrypt sensitive fields in an object
  decryptSensitiveFields(data, sensitiveFields = []) {
    const result = { ...data };
    
    for (const field of sensitiveFields) {
      if (result[field] && typeof result[field] === 'object' && result[field].encrypted) {
        try {
          result[field] = this.decrypt(result[field]);
        } catch (error) {
          console.warn(`Failed to decrypt field ${field}:`, error.message);
          result[field] = '[ENCRYPTED_DATA]';
        }
      }
    }
    
    return result;
  }

  // Hash sensitive data (one-way, for comparison purposes)
  hash(data, salt = null) {
    if (!salt) {
      salt = crypto.randomBytes(16).toString('hex');
    }
    
    const hash = crypto.pbkdf2Sync(data, salt, 10000, 64, 'sha512').toString('hex');
    
    return {
      hash,
      salt
    };
  }

  // Verify hashed data
  verifyHash(data, hashedData) {
    const { hash } = this.hash(data, hashedData.salt);
    return hash === hashedData.hash;
  }

  // Generate secure random tokens
  generateSecureToken(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  // Secure file operations
  encryptFile(filePath, outputPath = null) {
    try {
      const data = fs.readFileSync(filePath, 'utf8');
      const encrypted = this.encrypt(data);
      
      const output = outputPath || filePath + '.encrypted';
      fs.writeFileSync(output, JSON.stringify(encrypted));
      
      return output;
    } catch (error) {
      console.error('File encryption error:', error);
      throw new Error('Failed to encrypt file');
    }
  }

  decryptFile(filePath, outputPath = null) {
    try {
      const encryptedData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      const decrypted = this.decrypt(encryptedData);
      
      if (outputPath) {
        fs.writeFileSync(outputPath, decrypted);
        return outputPath;
      }
      
      return decrypted;
    } catch (error) {
      console.error('File decryption error:', error);
      throw new Error('Failed to decrypt file');
    }
  }

  // Secure conversation storage
  encryptConversation(conversation) {
    const sensitiveFields = ['userMessage', 'aiResponse', 'leadData'];
    return this.encryptSensitiveFields(conversation, sensitiveFields);
  }

  decryptConversation(encryptedConversation) {
    const sensitiveFields = ['userMessage', 'aiResponse', 'leadData'];
    return this.decryptSensitiveFields(encryptedConversation, sensitiveFields);
  }

  // Secure user data storage
  encryptUserData(userData) {
    const sensitiveFields = ['email', 'phone', 'personalInfo'];
    return this.encryptSensitiveFields(userData, sensitiveFields);
  }

  decryptUserData(encryptedUserData) {
    const sensitiveFields = ['email', 'phone', 'personalInfo'];
    return this.decryptSensitiveFields(encryptedUserData, sensitiveFields);
  }

  // Data anonymization for logs
  anonymizeForLogging(data) {
    if (typeof data !== 'object' || data === null) {
      return '[REDACTED]';
    }

    const anonymized = {};
    const sensitivePatterns = [
      /email/i,
      /password/i,
      /phone/i,
      /ssn/i,
      /credit/i,
      /card/i,
      /token/i,
      /key/i,
      /secret/i
    ];

    for (const [key, value] of Object.entries(data)) {
      const isSensitive = sensitivePatterns.some(pattern => pattern.test(key));
      
      if (isSensitive) {
        anonymized[key] = '[REDACTED]';
      } else if (typeof value === 'string' && value.length > 100) {
        anonymized[key] = value.substring(0, 100) + '...[TRUNCATED]';
      } else if (typeof value === 'object') {
        anonymized[key] = this.anonymizeForLogging(value);
      } else {
        anonymized[key] = value;
      }
    }

    return anonymized;
  }
}

// Create singleton instance
const encryptionService = new EncryptionService();

module.exports = {
  encryptionService,
  encrypt: encryptionService.encrypt.bind(encryptionService),
  decrypt: encryptionService.decrypt.bind(encryptionService),
  encryptConversation: encryptionService.encryptConversation.bind(encryptionService),
  decryptConversation: encryptionService.decryptConversation.bind(encryptionService),
  anonymizeForLogging: encryptionService.anonymizeForLogging.bind(encryptionService)
};
