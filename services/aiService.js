const OpenAI = require('openai');
const Anthropic = require('@anthropic-ai/sdk');

class AIService {
  constructor() {
    this.primaryProvider = process.env.PRIMARY_AI_PROVIDER || 'openai';
    this.enableFallback = process.env.ENABLE_AI_FALLBACK === 'true';
    this.mockMode = process.env.MOCK_MODE === 'true';
    
    // Initialize OpenAI client
    this.openai = null;
    if (process.env.OPENAI_API_KEY) {
      this.openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY
      });
    }
    
    // Initialize Claude client
    this.claude = null;
    if (process.env.CLAUDE_API_KEY) {
      this.claude = new Anthropic({
        apiKey: process.env.CLAUDE_API_KEY
      });
    }
    
    this.lastUsedProvider = null;
    this.fallbackReason = null;
  }

  /**
   * Determines if an error indicates the provider is down/unavailable
   */
  isProviderUnavailable(error) {
    if (!error) return false;

    // OpenAI specific errors
    if (error.status) {
      // 5xx server errors, 429 rate limit, 503 service unavailable, 502 bad gateway
      if (error.status >= 500 || error.status === 429 || error.status === 502) {
        return true;
      }
    }

    // Connection errors
    if (error.code === 'ECONNREFUSED' ||
        error.code === 'ENOTFOUND' ||
        error.code === 'ETIMEDOUT' ||
        error.code === 'ECONNRESET' ||
        error.code === 'EPIPE' ||
        error.message?.includes('network') ||
        error.message?.includes('timeout') ||
        error.message?.includes('connection') ||
        error.message?.includes('socket') ||
        error.message?.includes('ECONNRESET')) {
      return true;
    }

    // Claude specific errors
    if (error.type === 'overloaded_error' ||
        error.type === 'api_error' ||
        error.type === 'rate_limit_error' ||
        error.status === 529) {
      return true;
    }

    // Generic API unavailability indicators
    if (error.message?.includes('unavailable') ||
        error.message?.includes('overloaded') ||
        error.message?.includes('rate limit') ||
        error.message?.includes('service temporarily')) {
      return true;
    }

    return false;
  }

  /**
   * Health check for a specific provider
   */
  async healthCheck(provider = 'openai') {
    try {
      if (provider === 'openai' && this.openai) {
        // Simple test request to OpenAI
        await this.openai.chat.completions.create({
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: 'test' }],
          max_tokens: 1
        });
        return { status: 'healthy', provider: 'openai' };
      } else if (provider === 'claude' && this.claude) {
        // Simple test request to Claude
        await this.claude.messages.create({
          model: 'claude-3-haiku-20240307',
          max_tokens: 1,
          messages: [{ role: 'user', content: 'test' }]
        });
        return { status: 'healthy', provider: 'claude' };
      } else {
        return { status: 'unavailable', provider, reason: 'Client not configured' };
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        provider,
        error: error.message,
        isUnavailable: this.isProviderUnavailable(error)
      };
    }
  }

  /**
   * Convert OpenAI messages format to Claude format
   */
  convertMessagesToClaudeFormat(messages) {
    const systemMessages = messages.filter(msg => msg.role === 'system');
    const conversationMessages = messages.filter(msg => msg.role !== 'system');
    
    // Claude expects system message as a separate parameter
    const system = systemMessages.length > 0 ? 
      systemMessages.map(msg => msg.content).join('\n\n') : 
      undefined;
    
    // Convert user/assistant messages
    const claudeMessages = conversationMessages.map(msg => ({
      role: msg.role === 'user' ? 'user' : 'assistant',
      content: msg.content
    }));
    
    return { system, messages: claudeMessages };
  }

  /**
   * Convert OpenAI parameters to Claude parameters
   */
  convertToClaudeParams(openaiParams) {
    const { messages, model, max_tokens, temperature, ...otherParams } = openaiParams;
    const { system, messages: claudeMessages } = this.convertMessagesToClaudeFormat(messages);
    
    // Map OpenAI models to Claude models
    const modelMapping = {
      'gpt-4': 'claude-3-5-sonnet-20241022',
      'gpt-4o': 'claude-3-5-sonnet-20241022',
      'gpt-4-turbo': 'claude-3-5-sonnet-20241022',
      'gpt-3.5-turbo': 'claude-3-haiku-20240307'
    };
    
    const claudeModel = modelMapping[model] || 'claude-3-5-sonnet-20241022';
    
    return {
      model: claudeModel,
      max_tokens: max_tokens || 1000,
      temperature: temperature || 0.7,
      system,
      messages: claudeMessages
    };
  }

  /**
   * Chat completion with automatic fallback
   */
  async chatCompletion(params) {
    if (this.mockMode) {
      return this.getMockChatResponse();
    }

    let primaryError = null;
    
    // Try primary provider first
    if (this.primaryProvider === 'openai' && this.openai) {
      try {
        const response = await this.openai.chat.completions.create(params);
        this.lastUsedProvider = 'openai';
        this.fallbackReason = null;
        return {
          provider: 'openai',
          response: response.choices[0].message.content,
          usage: response.usage
        };
      } catch (error) {
        console.error('OpenAI chat completion error:', error);
        primaryError = error;
        
        if (!this.enableFallback || !this.isProviderUnavailable(error)) {
          throw error;
        }
      }
    } else if (this.primaryProvider === 'claude' && this.claude) {
      try {
        const claudeParams = this.convertToClaudeParams(params);
        const response = await this.claude.messages.create(claudeParams);
        this.lastUsedProvider = 'claude';
        this.fallbackReason = null;
        return {
          provider: 'claude',
          response: response.content[0].text,
          usage: response.usage
        };
      } catch (error) {
        console.error('Claude chat completion error:', error);
        primaryError = error;
        
        if (!this.enableFallback || !this.isProviderUnavailable(error)) {
          throw error;
        }
      }
    }

    // Try fallback provider
    if (this.enableFallback && primaryError) {
      const fallbackProvider = this.primaryProvider === 'openai' ? 'claude' : 'openai';
      
      if (fallbackProvider === 'claude' && this.claude) {
        try {
          console.log('Falling back to Claude due to OpenAI error');
          const claudeParams = this.convertToClaudeParams(params);
          const response = await this.claude.messages.create(claudeParams);
          this.lastUsedProvider = 'claude';
          this.fallbackReason = `OpenAI unavailable: ${primaryError.message}`;
          return {
            provider: 'claude',
            response: response.content[0].text,
            usage: response.usage,
            fallback: true,
            fallbackReason: this.fallbackReason
          };
        } catch (fallbackError) {
          console.error('Claude fallback also failed:', fallbackError);
          throw new Error(`Both providers failed. OpenAI: ${primaryError.message}, Claude: ${fallbackError.message}`);
        }
      } else if (fallbackProvider === 'openai' && this.openai) {
        try {
          console.log('Falling back to OpenAI due to Claude error');
          const response = await this.openai.chat.completions.create(params);
          this.lastUsedProvider = 'openai';
          this.fallbackReason = `Claude unavailable: ${primaryError.message}`;
          return {
            provider: 'openai',
            response: response.choices[0].message.content,
            usage: response.usage,
            fallback: true,
            fallbackReason: this.fallbackReason
          };
        } catch (fallbackError) {
          console.error('OpenAI fallback also failed:', fallbackError);
          throw new Error(`Both providers failed. Claude: ${primaryError.message}, OpenAI: ${fallbackError.message}`);
        }
      }
    }

    // If we get here, no providers are available
    throw primaryError || new Error('No AI providers available');
  }

  /**
   * Speech to text (OpenAI only for now)
   */
  async speechToText(audioStream, options = {}) {
    if (this.mockMode) {
      return {
        text: "This is a mock transcription for testing purposes.",
        provider: 'mock'
      };
    }

    if (!this.openai) {
      throw new Error('Speech-to-text service unavailable: OpenAI client not configured. Please check your API key.');
    }

    try {
      const transcription = await this.openai.audio.transcriptions.create({
        file: audioStream,
        model: 'whisper-1',
        language: options.language || 'en'
      });

      return {
        text: transcription.text,
        provider: 'openai'
      };
    } catch (error) {
      console.error('Speech-to-text error:', error);

      if (this.isProviderUnavailable(error)) {
        // For speech services, we don't have a fallback provider yet
        // In the future, we could integrate with other speech services like Google Speech-to-Text
        throw new Error('Speech-to-text service temporarily unavailable. Please try typing your message instead.');
      }

      throw error;
    }
  }

  /**
   * Text to speech (OpenAI only for now)
   */
  async textToSpeech(text, options = {}) {
    if (this.mockMode) {
      // Return a small silent audio buffer for testing
      return {
        buffer: Buffer.from([]),
        provider: 'mock'
      };
    }

    if (!this.openai) {
      throw new Error('Text-to-speech service unavailable: OpenAI client not configured. Please check your API key.');
    }

    try {
      const mp3 = await this.openai.audio.speech.create({
        model: options.model || 'tts-1',
        voice: options.voice || 'alloy',
        input: text,
        speed: options.speed || 1.0
      });

      const buffer = Buffer.from(await mp3.arrayBuffer());
      return {
        buffer,
        provider: 'openai'
      };
    } catch (error) {
      console.error('Text-to-speech error:', error);

      if (this.isProviderUnavailable(error)) {
        // For speech services, we don't have a fallback provider yet
        // In the future, we could integrate with other TTS services like Google Text-to-Speech
        throw new Error('Text-to-speech service temporarily unavailable. The text response is still available above.');
      }

      throw error;
    }
  }

  /**
   * Get mock response for testing
   */
  getMockChatResponse() {
    const mockResponses = [
      "Thank you for your interest in our investment services. I'd be happy to discuss how we can help you achieve your financial goals.",
      "Based on current market conditions, there are several opportunities worth exploring. Would you like to schedule a consultation?",
      "I understand you're looking for investment advice. Let me share some insights about the current market trends.",
      "That's a great question about portfolio diversification. Let me explain some strategies that might work for your situation."
    ];
    
    const randomResponse = mockResponses[Math.floor(Math.random() * mockResponses.length)];
    
    return {
      provider: 'mock',
      response: randomResponse,
      usage: { total_tokens: 50 }
    };
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      primaryProvider: this.primaryProvider,
      enableFallback: this.enableFallback,
      mockMode: this.mockMode,
      lastUsedProvider: this.lastUsedProvider,
      fallbackReason: this.fallbackReason,
      openaiAvailable: !!this.openai,
      claudeAvailable: !!this.claude
    };
  }
}

module.exports = AIService;
