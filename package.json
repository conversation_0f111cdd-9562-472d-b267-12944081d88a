{"name": "shareflix-voice-platform", "version": "1.0.0", "description": "ShareFlix voice-driven investment discovery platform using OpenAI and Claude", "main": "index.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["voice", "ai", "leads", "stock", "openai", "speech"], "author": "", "license": "ISC", "dependencies": {"@anthropic-ai/sdk": "^0.59.0", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "fluent-ffmpeg": "^2.1.3", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "node-wav": "^0.0.2", "openai": "^4.20.1", "validator": "^13.15.15", "xss": "^1.0.15"}, "devDependencies": {"nodemon": "^3.0.1"}}