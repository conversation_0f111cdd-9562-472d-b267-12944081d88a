# Echo - Voice Lead Experience

A voice-powered lead qualification system for stock investment businesses using OpenAI's GPT-4, Whisper, and TTS APIs.

## Features

- 🎤 **Voice Input**: Real-time speech-to-text using OpenAI Whisper
- 🤖 **AI Conversation**: Intelligent lead qualification with GPT-4
- 🔊 **Voice Output**: Natural text-to-speech responses using OpenAI TTS
- 📊 **Lead Tracking**: Automatic lead information extraction and storage
- 💼 **Stock Business Focus**: Specialized prompts for investment lead qualification

## Quick Start

### Prerequisites

- Node.js (v14 or higher)
- OpenAI API key with credits

### Installation

1. **Clone or download this project**

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up your OpenAI API key**:
   - Get your API key from https://platform.openai.com/api-keys
   - Copy `.env.example` to `.env`
   - Replace `your_openai_api_key_here` with your actual API key:
   ```
   OPENAI_API_KEY=sk-your-actual-api-key-here
   ```

4. **Start the server**:
   ```bash
   npm start
   ```

5. **Open your browser** and go to http://localhost:3000

## How It Works

1. **User clicks microphone** and speaks their message
2. **Whisper API** converts speech to text
3. **GPT-4** generates intelligent response based on stock business context
4. **TTS API** converts response to speech and plays it back
5. **Lead information** is automatically extracted and stored

## API Endpoints

- `POST /api/speech-to-text` - Convert audio to text
- `POST /api/chat` - Get AI response for conversation
- `POST /api/text-to-speech` - Convert text to speech
- `POST /api/save-lead` - Save lead information

## Project Structure

```
Echo/
├── server.js              # Express server with OpenAI integration
├── prompts.js            # AI prompts for stock business context
├── package.json          # Dependencies and scripts
├── public/               # Frontend files
│   ├── index.html       # Main UI
│   ├── styles.css       # Styling
│   └── app.js           # Frontend JavaScript
├── uploads/             # Temporary audio file storage
└── leads.json           # Lead data storage (created automatically)
```

## Configuration

### Environment Variables

- `OPENAI_API_KEY` - Your OpenAI API key (required)
- `PORT` - Server port (default: 3000)
- `NODE_ENV` - Environment (development/production)

### AI Configuration

The system uses:
- **GPT-4** for conversational AI
- **Whisper-1** for speech recognition
- **TTS-1** with "alloy" voice for speech synthesis

## Lead Qualification

The AI is designed to:
- Build rapport with potential clients
- Assess investment experience and goals
- Gather contact information naturally
- Identify qualified leads for consultation
- Provide valuable market insights

## Costs

Approximate OpenAI API costs:
- **Whisper**: $0.006 per minute of audio
- **GPT-4**: ~$0.03 per 1K tokens
- **TTS**: $0.015 per 1K characters

Typical conversation costs: $0.10 - $0.50

## Browser Compatibility

- Chrome/Edge: Full support
- Firefox: Full support
- Safari: Full support
- Mobile browsers: Supported with microphone access

## Security Notes

- Never commit your `.env` file with real API keys
- The `.env` file is already in `.gitignore`
- Lead data is stored locally in `leads.json`
- Consider database integration for production use

## Troubleshooting

### Common Issues

1. **Microphone not working**:
   - Ensure browser has microphone permissions
   - Check if HTTPS is required (some browsers)

2. **API errors**:
   - Verify OpenAI API key is correct
   - Check OpenAI account has sufficient credits
   - Ensure internet connection is stable

3. **Audio playback issues**:
   - Check browser audio permissions
   - Verify speakers/headphones are working

### Development

To run in development mode with auto-restart:
```bash
npm install -g nodemon
nodemon server.js
```

## Next Steps

- [ ] Add database integration for lead storage
- [ ] Implement user authentication
- [ ] Add conversation analytics
- [ ] Create admin dashboard for lead management
- [ ] Add email integration for lead follow-up
- [ ] Implement conversation transcription export

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Verify your OpenAI API key and credits
3. Check browser console for error messages

## License

This project is for business use. Customize as needed for your stock investment business.
